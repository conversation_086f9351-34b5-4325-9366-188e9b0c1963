# 医疗信息系统整合项目 - 产品需求文档

## 1. 产品概述

本项目旨在整合现有的HIS临床病历系统(demo.1)和RIS影像报告系统(demo_RIS)，创建一个统一的医疗信息管理平台。通过角色切换功能，为临床医生和影像科医生提供专业化的工作界面，提升医疗工作效率和用户体验。

该平台将解决医疗机构中不同科室使用独立系统导致的信息孤岛问题，为医生提供一站式的医疗信息管理解决方案。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 临床医生 | 系统预设角色 | 可访问病历录入模块，进行患者信息管理和病历编写 |
| 影像科医生 | 系统预设角色 | 可访问影像报告模块，进行影像查看和报告编写 |

### 2.2 功能模块

本医疗信息系统整合项目包含以下主要页面：
1. **主页面**: 角色选择tab、统一导航栏、内容展示区域
2. **临床医生工作区**: 病历录入表单、患者信息管理
3. **影像科医生工作区**: 患者列表、影像查看、报告编写

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 主页面 | 角色切换Tab | 提供临床医生和影像科医生两个tab选项，支持角色快速切换 |
| 主页面 | 统一导航栏 | 显示当前用户角色、系统标题、基础操作按钮 |
| 主页面 | 内容展示区域 | 根据选中的tab动态加载对应的功能模块 |
| 临床医生工作区 | 患者信息表单 | 录入患者基本信息：姓名、性别、年龄、ID、联系方式等 |
| 临床医生工作区 | 病史记录模块 | 记录主诉、现病史、既往史、个人史、家族史等详细信息 |
| 临床医生工作区 | 检查诊断模块 | 填写查体结果、辅助检查、诊断结论、处理方案等 |
| 临床医生工作区 | 数据管理功能 | 支持患者数据切换、保存提交、外部API集成 |
| 影像科医生工作区 | 患者列表面板 | 显示患者列表、支持搜索筛选、患者基本信息展示 |
| 影像科医生工作区 | 报告编写面板 | 结构化报告编写：检查所见、诊断意见、建议三部分 |
| 影像科医生工作区 | 模板系统 | 提供多种检查类型模板：胸部CT、腹部MRI、头颅CT、膝关节MRI |
| 影像科医生工作区 | 影像查看功能 | 集成DICOM查看器，支持影像浏览和基础操作 |

## 3. 核心流程

### 临床医生工作流程
1. 用户进入系统，选择"临床医生"tab
2. 在患者信息表单中选择或录入患者基本信息
3. 依次填写病史记录：主诉、现病史、既往史等
4. 完成检查诊断：查体、辅助检查、诊断、处理
5. 保存病历数据并提交到外部系统
6. 支持切换其他患者继续录入

### 影像科医生工作流程
1. 用户进入系统，选择"影像科医生"tab
2. 从患者列表中搜索并选择目标患者
3. 查看患者影像资料（通过DICOM查看器）
4. 选择合适的报告模板或创建新报告
5. 编写结构化影像报告：检查所见、诊断意见、建议
6. 设置危机值和HP检测状态
7. 保存报告并支持打印输出

```mermaid
graph TD
  A[系统主页] --> B[角色选择Tab]
  B --> C[临床医生工作区]
  B --> D[影像科医生工作区]
  C --> E[病历录入]
  C --> F[患者管理]
  D --> G[患者列表]
  D --> H[影像查看]
  D --> I[报告编写]
```

## 4. 用户界面设计

### 4.1 设计风格
- **主色调**: 医疗蓝色系 (#3B82F6) 和白色 (#FFFFFF)
- **辅助色**: 灰色系 (#6B7280, #F3F4F6) 用于背景和边框
- **按钮样式**: 圆角按钮设计，支持悬停和点击状态
- **字体**: 系统默认字体，主要内容14px，标题16-18px
- **布局风格**: 卡片式布局，顶部导航 + tab切换 + 内容区域
- **图标风格**: 使用Lucide React图标库，简洁现代的线性图标

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 主页面 | 顶部导航栏 | 蓝色背景，白色文字，系统标题居中，用户信息右对齐 |
| 主页面 | Tab切换区域 | 水平tab布局，选中状态蓝色底色，未选中灰色文字 |
| 主页面 | 内容展示区域 | 白色背景，圆角卡片容器，阴影效果 |
| 临床医生工作区 | 表单区域 | 网格布局，标签左对齐，输入框统一样式，必填项红色星号 |
| 临床医生工作区 | 操作按钮 | 主要按钮蓝色背景，次要按钮白色背景蓝色边框 |
| 影像科医生工作区 | 三栏布局 | 左侧患者列表，中间报告编写，右侧工具栏 |
| 影像科医生工作区 | 患者列表 | 卡片式列表，悬停高亮，选中状态蓝色边框 |
| 影像科医生工作区 | 报告编辑器 | 文本域组件，标题分区，实时保存提示 |

### 4.3 响应式设计
本产品采用桌面优先的设计策略，主要面向医院工作站使用。在平板设备上进行适配优化，支持触摸操作。移动端提供基础的查看功能，但主要工作流程仍建议在桌面端完成。