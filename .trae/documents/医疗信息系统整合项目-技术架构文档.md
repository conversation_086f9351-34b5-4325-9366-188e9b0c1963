# 医疗信息系统整合项目 - 技术架构文档

## 1. 架构设计

```mermaid
graph TD
  A[用户浏览器] --> B[React 前端应用]
  B --> C[Zustand 状态管理]
  B --> D[React Router 路由]
  C --> E[HIS模块状态]
  C --> F[RIS模块状态]
  B --> G[外部API服务]
  
  subgraph "前端层"
    B
    C
    D
  end
  
  subgraph "状态管理层"
    E
    F
  end
  
  subgraph "外部服务"
    G
  end
```

## 2. 技术描述

- 前端: React@18 + TypeScript + Vite + Tailwind CSS + Zustand + React Router DOM
- 状态管理: Zustand (全局状态) + React useState (局部状态)
- UI组件: Lucide React (图标) + Sonner (通知)
- 构建工具: Vite + TypeScript + ESLint

## 3. 路由定义

| 路由 | 用途 |
|------|------|
| / | 主页面，包含角色切换tab和对应的工作区域 |
| /clinical | 临床医生工作区（可选的独立路由） |
| /radiology | 影像科医生工作区（可选的独立路由） |
| /dicom-viewer | DICOM影像查看器页面 |

## 4. API定义

### 4.1 核心API

#### 病历数据提交相关
```
POST /api/patient/save
```

请求参数:
| 参数名称 | 参数类型 | 是否必需 | 描述 |
|----------|----------|----------|------|
| patientData | PatientRecord | true | 完整的患者病历数据 |
| source | string | true | 数据来源标识 |

响应参数:
| 参数名称 | 参数类型 | 描述 |
|----------|----------|------|
| success | boolean | 提交是否成功 |
| message | string | 响应消息 |

请求示例:
```json
{
  "patientData": {
    "name": "张三",
    "gender": "男",
    "age": 45,
    "id": "123456789",
    "complaint": "胸痛3天",
    "history": "患者3天前无明显诱因出现胸痛..."
  },
  "source": "clinical_system"
}
```

#### 影像报告保存相关
```
POST /api/report/save
```

请求参数:
| 参数名称 | 参数类型 | 是否必需 | 描述 |
|----------|----------|----------|------|
| reportData | MedicalReport | true | 影像报告数据 |
| patientId | string | true | 患者ID |

响应参数:
| 参数名称 | 参数类型 | 描述 |
|----------|----------|------|
| success | boolean | 保存是否成功 |
| reportId | string | 报告ID |

## 5. 服务架构图

```mermaid
graph TD
  A[客户端/前端] --> B[路由控制层]
  B --> C[组件服务层]
  C --> D[状态管理层]
  D --> E[数据持久化层]
  
  subgraph 服务端
    B
    C
    D
    E
  end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
  PATIENT ||--o{ MEDICAL_RECORD : has
  PATIENT ||--o{ MEDICAL_IMAGE : has
  PATIENT ||--o{ MEDICAL_REPORT : has
  MEDICAL_REPORT ||--|| REPORT_TEMPLATE : uses
  
  PATIENT {
    string id PK
    string name
    string gender
    number age
    string phone
    string address
    date createdAt
  }
  
  MEDICAL_RECORD {
    string id PK
    string patientId FK
    string complaint
    string history
    string examination
    string diagnosis
    string treatment
    date createdAt
  }
  
  MEDICAL_IMAGE {
    string id PK
    string patientId FK
    string studyId
    string seriesId
    string imageUrl
    string modality
    date studyDate
  }
  
  MEDICAL_REPORT {
    string id PK
    string patientId FK
    string findings
    string diagnosis
    string suggestions
    boolean isCritical
    boolean hpDetected
    date createdAt
  }
  
  REPORT_TEMPLATE {
    string id PK
    string name
    string category
    string template
    date createdAt
  }
```

### 6.2 数据定义语言

#### 患者表 (patients)
```sql
-- 创建患者表
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10) NOT NULL,
    age INTEGER NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_patients_name ON patients(name);
CREATE INDEX idx_patients_created_at ON patients(created_at DESC);

-- 初始化数据
INSERT INTO patients (name, gender, age, phone, address) VALUES
('张三', '男', 45, '13800138001', '北京市朝阳区'),
('李四', '女', 32, '13800138002', '上海市浦东新区'),
('王五', '男', 58, '13800138003', '广州市天河区'),
('赵六', '女', 28, '13800138004', '深圳市南山区');
```

#### 病历记录表 (medical_records)
```sql
-- 创建病历记录表
CREATE TABLE medical_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    complaint TEXT,
    present_history TEXT,
    past_history TEXT,
    personal_history TEXT,
    family_history TEXT,
    physical_examination TEXT,
    auxiliary_examination TEXT,
    diagnosis TEXT,
    treatment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_medical_records_patient_id ON medical_records(patient_id);
CREATE INDEX idx_medical_records_created_at ON medical_records(created_at DESC);
```

#### 医学影像表 (medical_images)
```sql
-- 创建医学影像表
CREATE TABLE medical_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    study_id VARCHAR(100) NOT NULL,
    series_id VARCHAR(100) NOT NULL,
    image_url TEXT NOT NULL,
    modality VARCHAR(10) NOT NULL,
    study_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_medical_images_patient_id ON medical_images(patient_id);
CREATE INDEX idx_medical_images_study_date ON medical_images(study_date DESC);
```

#### 医学报告表 (medical_reports)
```sql
-- 创建医学报告表
CREATE TABLE medical_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    findings TEXT NOT NULL,
    diagnosis TEXT NOT NULL,
    suggestions TEXT,
    is_critical BOOLEAN DEFAULT FALSE,
    hp_detected BOOLEAN DEFAULT FALSE,
    template_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_medical_reports_patient_id ON medical_reports(patient_id);
CREATE INDEX idx_medical_reports_created_at ON medical_reports(created_at DESC);
CREATE INDEX idx_medical_reports_is_critical ON medical_reports(is_critical);
```

#### 报告模板表 (report_templates)
```sql
-- 创建报告模板表
CREATE TABLE report_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    findings_template TEXT,
    diagnosis_template TEXT,
    suggestions_template TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_report_templates_category ON report_templates(category);

-- 初始化模板数据
INSERT INTO report_templates (name, category, findings_template, diagnosis_template, suggestions_template) VALUES
('胸部CT模板', 'CT', '双肺纹理清晰，未见明显实质性病变...', '双肺未见异常', '建议定期复查'),
('腹部MRI模板', 'MRI', '肝脏形态大小正常，信号均匀...', '腹部脏器未见异常', '注意饮食，定期体检'),
('头颅CT模板', 'CT', '脑实质密度均匀，脑沟脑裂未见异常...', '颅脑未见异常', '避免头部外伤'),
('膝关节MRI模板', 'MRI', '膝关节诸骨质未见异常，关节间隙正常...', '膝关节未见异常', '适当运动，避免剧烈活动');
```