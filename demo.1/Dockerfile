# Use the official Node.js image as the base image
FROM node:18 AS build
# Set the working directory inside the container
WORKDIR /app
# Copy package.json and package-lock.json to the container
COPY package.json package-lock.json ./
# Install dependencies
RUN npm install
# Copy the rest of the application code to the container
COPY . .
# Build the Vite app
RUN npm run build
# Use a lightweight web server to serve the built files
FROM nginx:alpine AS production
# Copy the built files from the previous stage
COPY --from=build /app/dist /usr/share/nginx/html

COPY nginx.conf /etc/nginx/conf.d/default.conf
# Expose port 80
EXPOSE 80
# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]