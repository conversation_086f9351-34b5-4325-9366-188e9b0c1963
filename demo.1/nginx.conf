server {
    listen 80;
    server_name localhost;

    # Serve the built Vite app
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # File server for ClickOnce deployment files
    location /watson/ {
        alias /watson/;
        autoindex on;
        autoindex_exact_size off;
        autoindex_format html;
        autoindex_localtime on;
        
        # Configure proper MIME types for ClickOnce publishing
        types {
            # Standard web file types
            text/html                             html htm shtml;
            text/css                              css;
            text/xml                              xml;
            image/gif                             gif;
            image/jpeg                            jpeg jpg;
            image/png                             png;
            
            # ClickOnce specific types
            application/x-ms-application          application;
            application/x-ms-manifest             manifest;
            application/octet-stream              deploy;
            application/vnd.ms.wlw-offline-file   wlw;
            application/x-silverlight-app         xap;
            application/x-ms-xbap                 xbap;
            application/msword                    doc;
            application/vnd.ms-excel              xls;
            application/vnd.ms-powerpoint         ppt;
            application/vnd.openxmlformats-officedocument.wordprocessingml.document    docx;
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet          xlsx;
            application/vnd.openxmlformats-officedocument.presentationml.presentation  pptx;
            application/x-msdownload              exe dll;
            application/x-msi                     msi;
            application/octet-stream              bin;
        }
    }
}