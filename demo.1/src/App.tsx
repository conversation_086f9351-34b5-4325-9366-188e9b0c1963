import React, { useEffect, useState } from 'react';
import { Save, ChevronRight } from 'lucide-react';

interface PatientRecord {
  hospitalName: string;
  patientName: string;
  gender: string;
  age: string;
  patientId: string;
  department: string;
  visitDate: string;
  chiefComplaint: string;
  presentIllness: string;
  pastHistory: {
    surgeryHistory: string;
    allergyHistory: string;
    transfusionHistory: string;
    vaccinationHistory: string;
    infectiousHistory: string;
  };
  menstrualHistory: string;
  physicalExam: string;
  auxiliaryExam: string;
  diagnosis: string;
  treatment: string[];
  followUpNotes: string;
}

const samplePatients: PatientRecord[] = [
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '张明',
    gender: '男',
    age: '45',
    patientId: '13391478',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '突发胸痛伴呼吸困难2小时',
    presentIllness: '患者2小时前无明显诱因出现剧烈胸痛，疼痛位于胸骨后，呈压榨性，伴有大汗、呼吸困难、恶心，症状持续不缓解，遂急诊就医',
    pastHistory: {
      surgeryHistory: '胆囊切除术（2022年）',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '不适用',
    physicalExam: 'T 36.8℃，P 110次/分，R 25次/分，BP 165/95mmHg。神志清楚，痛苦貌，大汗淋漓。双肺呼吸音粗，未闻及明显啰音。心率110次/分，律不齐，可闻及第三心音。腹软，无压痛。双下肢无水肿。',
    auxiliaryExam: 'ECG：窦性心动过速，V1-V4导联ST段抬高>2mm；\n心肌酶谱：肌钙蛋白I 2.5ng/ml↑；\nBNP：560pg/ml↑；\n血常规：WBC 12.5×10^9/L↑',
    diagnosis: '急性前壁心肌梗死（发病2小时）',
    treatment: [
      '1. 立即给予吸氧、建立静脉通路',
      '2. 急诊冠状动脉造影+PCI手术',
      '3. 术前给予：',
      '   - 阿司匹林300mg嚼服',
      '   - 氯吡格雷600mg负荷量',
      '   - 肝素5000U静推',
      '4. 持续心电监护',
      '5. 建议立即住院治疗'
    ],
    followUpNotes: '已通知心导管室准备，家属已签署手术同意书'
  },
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '李强',
    gender: '男',
    age: '58',
    patientId: '13391481',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '车祸致左侧肢体疼痛、活动受限15分钟',
    presentIllness: '患者15分钟前驾驶电动车与机动车相撞，致左侧肢体疼痛，活动受限。现场目击者称事故后曾短暂意识丧失约1分钟。',
    pastHistory: {
      surgeryHistory: '无',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '不适用',
    physicalExam: 'T 36.7℃，P 95次/分，R 22次/分，BP 145/85mmHg。神志清楚，左额见3cm裂伤，左肩、左髋部压痛明显，左下肢活动受限，足背动脉搏动存在。',
    auxiliaryExam: '全身CT：左侧肱骨干骨折，左侧髋臼骨折，左侧胫腓骨骨折；\n头颅CT：未见明显异常；\n胸腹部CT：未见明显异常；\n血常规：正常范围',
    diagnosis: '多发性骨折（左肱骨、左髋臼、左胫腓骨）',
    treatment: [
      '1. 立即建立静脉通路',
      '2. 止痛：曲美布汀100mg静滴',
      '3. 患肢固定',
      '4. 创面处理',
      '5. 骨科会诊',
      '6. 建议立即住院手术'
    ],
    followUpNotes: '已通知骨科准备手术'
  },
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '王丽',
    gender: '女',
    age: '40',
    patientId: '13391482',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '持续性腹痛伴发热、呕吐12小时',
    presentIllness: '患者12小时前无明显诱因出现上腹部持续性疼痛，呈持续性，伴恶心呕吐3次，发热，体温最高38.5℃。既往体健。',
    pastHistory: {
      surgeryHistory: '无',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '月经规律',
    physicalExam: 'T 38.3℃，P 102次/分，R 20次/分，BP 125/75mmHg。神志清楚，上腹部压痛、反跳痛阳性，墨菲氏征阳性。',
    auxiliaryExam: '血常规：WBC 15.6×10^9/L↑，N 85%↑；\n血淀粉酶：正常；\n腹部B超：胆囊增大，壁厚，内见多发结石，周围见液性暗区；\n肝功能：转氨酶轻度升高',
    diagnosis: '急性胆囊炎',
    treatment: [
      '1. 禁食、胃肠减压',
      '2. 抗感染：头孢呋辛1.5g q8h 静滴',
      '3. 解痉止痛：654-2 50mg 肌注',
      '4. 补液：生理盐水500ml 静滴',
      '5. 普外科会诊',
      '6. 建议急诊手术'
    ],
    followUpNotes: '已通知普外科准备手术'
  },
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '赵华',
    gender: '女',
    age: '45',
    patientId: '13391483',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '突发左侧肢体活动障碍、言语不清2小时',
    presentIllness: '患者2小时前突发左侧肢体无力，不能站立行走，言语不清。既往有高血压病史5年，血压控制欠佳。',
    pastHistory: {
      surgeryHistory: '无',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '绝经2年',
    physicalExam: 'T 36.5℃，P 88次/分，R 18次/分，BP 185/100mmHg。神志清楚，言语不清，左侧肢体肌力II级，病理征阳性。',
    auxiliaryExam: '头颅CT：右侧大脑中动脉区见片状低密度影；\nCT血管造影：右侧大脑中动脉M1段闭塞；\n心电图：窦性心律；\n血常规、凝血功能：正常范围',
    diagnosis: '急性缺血性脑卒中',
    treatment: [
      '1. 立即建立静脉通路',
      '2. 急诊静脉溶栓：',
      '   - rt-PA 0.9mg/kg',
      '   - 10%剂量静脉推注，余量持续泵入1小时',
      '3. 降压治疗：乌拉地尔10mg缓慢静推',
      '4. 神经内科会诊',
      '5. 建议立即住院'
    ],
    followUpNotes: '已收入卒中单元'
  }
];

function App() {
  const [currentPatientIndex, setCurrentPatientIndex] = useState(0);
  const [record, setRecord] = useState<PatientRecord>(samplePatients[0]);
  const [sourceValue, setSourceValue] = useState(''); // Add state for sourceValue

  // Add useEffect to load source value from cookie on component mount
  useEffect(() => {
    const cookies = document.cookie.split('; ');
    const sourceCookie = cookies.find(cookie => cookie.startsWith('SOURCE='));
    if (sourceCookie) {
      const value = sourceCookie.split('=')[1];
      setSourceValue(decodeURIComponent(value));
    }
  }, []);

  // Add handler for source value changes
  const handleSourceValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSourceValue(value);
    // Save to cookie with 1 year expiration
    document.cookie = `SOURCE=${encodeURIComponent(value)}; path=/; max-age=31536000`;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setRecord(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePastHistoryChange = (field: keyof typeof record.pastHistory, value: string) => {
    setRecord(prev => ({
      ...prev,
      pastHistory: {
        ...prev.pastHistory,
        [field]: value
      }
    }));
  };

  const handleSave = () => {
    //const baseUrl='https://localhost:44324/api/signals/v1/submit';
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const baseUrl = ((window as any).WATSON_API_BASE_URL) || "http://*************:27861/api/signals/v1/submit";

    const request ={
      source: '',
      name: record.patientName,
      gender: record.gender,
      age: record.age,
      patientId: record.patientId,
      department: record.department,
      visitDate: record.visitDate,
      complaints: record.chiefComplaint,
      abstractHistory: `${record.presentIllness} 手术史：${record.pastHistory.surgeryHistory}，过敏史：${record.pastHistory.allergyHistory}，输血史：${record.pastHistory.transfusionHistory}，预防接种史：${record.pastHistory.vaccinationHistory}，传染病史：${record.pastHistory.infectiousHistory}`,
      diagnosis: record.diagnosis,
    };

    //get from cookie SOURCE
    const cookies = document.cookie.split('; ');
    const sourceCookie = cookies.find(cookie => cookie.startsWith('SOURCE='));
    if (sourceCookie) {
      const sourceValue = sourceCookie.split('=')[1];
      request.source = decodeURIComponent(sourceValue);
    }

    console.log('Sending patient data:', request);
  
    fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    alert('患者记录已成功保存！');
  })  
  .catch(error => {
    console.error('Error saving patient data:', error);
    alert('保存失败，请重试');
  });
    

  };

  const handleNext = () => {
    const nextIndex = (currentPatientIndex + 1) % samplePatients.length;
    setCurrentPatientIndex(nextIndex);
    setRecord(samplePatients[nextIndex]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-blue-600 text-white px-6 py-2 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold">医院信息系统 (HIS)</h1>
          <div className="flex gap-2">
            <button 
              onClick={handleSave}
              className="flex items-center px-3 py-1 bg-green-600 rounded hover:bg-green-700 transition"
            >
              <Save className="w-4 h-4 mr-1" />
              保存
            </button>
            <button 
              onClick={handleNext}
              className="flex items-center px-3 py-1 bg-blue-700 rounded hover:bg-blue-800 transition"
            >
              <ChevronRight className="w-4 h-4 mr-1" />
              下一个
            </button>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-4 max-w-4xl">
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="text-center mb-4 border-b pb-2">
            <h1 className="text-xl font-bold text-blue-800">{record.hospitalName}</h1>
            <h2 className="text-lg mt-1 text-gray-700">门诊病历</h2>
          </div>

          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="flex items-center gap-2 col-span-2">
                <label className="text-sm font-medium text-gray-700 w-16">来源</label>
                <input
                  type="text"
                  name="sourceValue"
                  value={sourceValue}
                  onChange={handleSourceValue}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 w-16">姓名</label>
                <input
                  type="text"
                  name="patientName"
                  value={record.patientName}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 w-16">性别</label>
                <select
                  name="gender"
                  value={record.gender}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="">请选择</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 w-16">年龄</label>
                <input
                  type="text"
                  name="age"
                  value={record.age}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 w-16">ID号</label>
                <input
                  type="text"
                  name="patientId"
                  value={record.patientId}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-2 col-span-2">
                <label className="text-sm font-medium text-gray-700 w-16">科室</label>
                <input
                  type="text"
                  name="department"
                  value={record.department}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-2 col-span-2">
                <label className="text-sm font-medium text-gray-700 w-16">就诊日期</label>
                <input
                  type="date"
                  name="visitDate"
                  value={record.visitDate}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16 pt-1">主诉</label>
                <textarea
                  name="chiefComplaint"
                  value={record.chiefComplaint}
                  onChange={handleChange}
                  rows={2}
                  className="flex-1 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="flex items-start gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16 pt-1">现病史</label>
                <textarea
                  name="presentIllness"
                  value={record.presentIllness}
                  onChange={handleChange}
                  rows={2}
                  className="flex-1 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="bg-gray-50 p-2 rounded">
                <div className="flex items-center gap-2 mb-2">
                  <label className="text-sm font-medium text-gray-700 w-16">既往史</label>
                </div>
                <div className="grid grid-cols-2 gap-2 pl-16">
                  {Object.entries(record.pastHistory).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2">
                      <label className="text-sm text-gray-600 w-24">
                        {key === 'surgeryHistory' && '手术史'}
                        {key === 'allergyHistory' && '过敏史'}
                        {key === 'transfusionHistory' && '输血史'}
                        {key === 'vaccinationHistory' && '预防接种史'}
                        {key === 'infectiousHistory' && '传染病史'}
                      </label>
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => handlePastHistoryChange(key as keyof typeof record.pastHistory, e.target.value)}
                        className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                      />
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16">月经史</label>
                <input
                  type="text"
                  name="menstrualHistory"
                  value={record.menstrualHistory}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="flex items-start gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16 pt-1">查体</label>
                <textarea
                  name="physicalExam"
                  value={record.physicalExam}
                  onChange={handleChange}
                  rows={2}
                  className="flex-1 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="flex items-start gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16 pt-1">辅助检查</label>
                <textarea
                  name="auxiliaryExam"
                  value={record.auxiliaryExam}
                  onChange={handleChange}
                  rows={2}
                  className="flex-1 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="flex items-center gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16">诊断</label>
                <input
                  type="text"
                  name="diagnosis"
                  value={record.diagnosis}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="flex items-start gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16 pt-1">处理</label>
                <textarea
                  name="treatment"
                  value={record.treatment.join('\n')}
                  onChange={(e) => setRecord(prev => ({ ...prev, treatment: e.target.value.split('\n') }))}
                  rows={3}
                  className="flex-1 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>

              <div className="flex items-center gap-2 bg-gray-50 p-2 rounded">
                <label className="text-sm font-medium text-gray-700 w-16">随诊</label>
                <input
                  type="text"
                  name="followUpNotes"
                  value={record.followUpNotes}
                  onChange={handleChange}
                  className="flex-1 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;