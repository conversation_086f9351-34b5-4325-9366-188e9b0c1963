// 患者信息类型定义
export interface Patient {
  id: string;
  name: string;
  age: number;
  gender: '男' | '女';
  examDate: string;
  examType: string;
  department: string; // 就诊科室
  studyId: string;
  chiefComplaint: string; // 主诉
  avatar?: string;
}

// 医学影像类型定义
export interface MedicalImage {
  id: string;
  patientId: string;
  seriesName: string;
  imageUrl: string;
  thumbnailUrl: string;
  sequenceNumber: number;
}

// 报告类型定义
export interface MedicalReport {
  id: string;
  patientId: string;
  findings: string; // 检查所见
  diagnosis: string; // 诊断意见
  suggestion: string; // 建议
  template: string; // 使用的模板
  criticalValue: '上报' | '不上报'; // 危机值：上报/不上报
  hpTest: '阳性' | '阴性'; // HP检测：阳性/阴性
  createdAt: string;
  updatedAt: string;
  status: 'draft' | 'completed' | 'reviewed';
}

// 报告模板类型定义
export interface ReportTemplate {
  id: string;
  name: string;
  examType: string;
  findingsTemplate: string;
  diagnosisTemplate: string;
  suggestionTemplate: string;
}

// 影像查看器设置
export interface ViewerSettings {
  zoom: number;
  windowWidth: number;
  windowCenter: number;
  brightness: number;
  contrast: number;
}

// DICOM 相关类型定义
export interface DicomFile {
  id: string;
  fileName: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata: any; // 实际项目中应为更具体的元数据类型
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  pixelData: any; // 实际项目中应为更具体的像素数据类型
  width: number;
  height: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getDisplayData: (options: any) => any;
}

export interface DicomSeries {
  seriesInstanceUID: string;
  seriesDescription: string;
  modality: string;
  files: DicomFile[];
}

export interface DicomStudy {
  studyInstanceUID: string;
  patientId: string;
  studyDate: string;
  studyDescription: string;
  series: DicomSeries[];
}
