import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRadiologyStore } from '@/store/useRadiologyStore';
import { DicomStudy, DicomSeries, DicomFile } from '@/types/radiology';
import { processUploadedFiles, DicomMetadata, convertToDisplayData, createMockDicomData } from '@/utils/dicomUtils';
import { initializeCornerstone, loadDicomImage, renderImageToCanvas } from '@/utils/cornerstoneUtils';
import { Upload, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, ArrowLeft, Folder, SkipBack, SkipForward, Play, Pause } from 'lucide-react';
import * as cornerstone from 'cornerstone-core';
import DwvDicomViewer from '@/components/DwvDicomViewer';

const DicomViewer: React.FC = () => {
  const navigate = useNavigate();
  const { selectedPatient, setSelectedPatient, currentReport, setCurrentReport, studies, setStudies } = useRadiologyStore();
  const [dicomStudies, setDicomStudies] = useState<DicomStudy[]>([]);
  const [currentStudy, setCurrentStudy] = useState<DicomStudy | null>(null);
  const [currentSeries, setCurrentSeries] = useState<DicomSeries | null>(null);
  const [currentDicomFile, setCurrentDicomFile] = useState<DicomFile | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [dicomMetadata, setDicomMetadata] = useState<DicomMetadata | null>(null);
  const [useDwvViewer, setUseDwvViewer] = useState(false);

  const [viewerSettings, setViewerSettings] = useState({
    zoom: 1,
    rotation: 0,
    panX: 0,
    panY: 0,
    windowWidth: 400,
    windowCenter: 40,
    invert: false
  });

  const [isPlaying, setIsPlaying] = useState(false);
  const [frameInfo, setFrameInfo] = useState<{
    currentFrame: number;
    totalFrames: number;
    frameRate: number;
  } | null>(null);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    initializeCornerstone();
    setLoading(false);
  }, []);

  // 从store获取studies数据，如果没有则初始化
  useEffect(() => {
    const initializeDicomData = async () => {
      if (studies && studies.length > 0) {
        console.log('从store获取到DICOM数据:', studies);
        setDicomStudies(studies);
        setCurrentStudy(studies[0]);
        if (studies[0].series.length > 0) {
          setCurrentSeries(studies[0].series[0]);
          if (studies[0].series[0].files.length > 0) {
            setCurrentDicomFile(studies[0].series[0].files[0]);
            setCurrentImageIndex(0);
          }
        }
      } else {
        console.log('Store中没有DICOM数据，开始初始化...');
        try {
          const mockStudies = await createMockDicomData();
          setStudies(mockStudies);
          console.log('DICOM数据初始化完成:', mockStudies);
        } catch (error) {
          console.error('初始化DICOM数据失败:', error);
          setError('初始化DICOM数据失败');
        }
      }
    };

    initializeDicomData();
  }, [studies, setStudies]);

  // 处理进度更新
  const handleProgress = useCallback((progress: number) => {
    setLoadingProgress(progress);
  }, []);

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    try {
      setIsUploading(true);
      setError(null);
      
      const fileArray = Array.from(files);
      const studies = await processUploadedFiles(fileArray);
      
      if (studies.length > 0) {
        setDicomStudies(studies);
        setCurrentStudy(studies[0]);
        if (studies[0].series.length > 0) {
          setCurrentSeries(studies[0].series[0]);
          if (studies[0].series[0].files.length > 0) {
            setCurrentDicomFile(studies[0].series[0].files[0]);
            setCurrentImageIndex(0);
          }
        }
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      setError('文件上传失败，请检查文件格式');
    } finally {
      setIsUploading(false);
      setLoadingProgress(0);
    }
  }, []);

  const handleStudySelect = (study: DicomStudy) => {
    setCurrentStudy(study);
    if (study.series.length > 0) {
      const firstSeries = study.series[0];
      setCurrentSeries(firstSeries);
      if (firstSeries.files.length > 0) {
        setCurrentDicomFile(firstSeries.files[0]);
        setCurrentImageIndex(0);
      }
    }
  };

  const handleSeriesSelect = (series: DicomSeries) => {
    setCurrentSeries(series);
    if (series.files.length > 0) {
      setCurrentDicomFile(series.files[0]);
      setCurrentImageIndex(0);
    }
  };

  const handleImageSelect = (file: DicomFile, index: number) => {
    setCurrentDicomFile(file);
    setCurrentImageIndex(index);
  };

  const handlePreviousImage = () => {
    if (!currentSeries || currentSeries.files.length === 0) return;
    const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : currentSeries.files.length - 1;
    setCurrentDicomFile(currentSeries.files[newIndex]);
    setCurrentImageIndex(newIndex);
  };

  const handleNextImage = () => {
    if (!currentSeries || currentSeries.files.length === 0) return;
    const newIndex = currentImageIndex < currentSeries.files.length - 1 ? currentImageIndex + 1 : 0;
    setCurrentDicomFile(currentSeries.files[newIndex]);
    setCurrentImageIndex(newIndex);
  };

  const renderDicomImage = useCallback(async () => {
    if (!currentDicomFile || !canvasRef.current) return;

    try {
      const canvas = canvasRef.current;
      cornerstone.enable(canvas);

      // Convert DicomFile to display data for cornerstone
      const displayData = convertToDisplayData(
        currentDicomFile.pixelData,
        currentDicomFile.width,
        currentDicomFile.height,
        {
          windowWidth: currentDicomFile.metadata.WindowWidth || viewerSettings.windowWidth,
          windowCenter: currentDicomFile.metadata.WindowCenter || viewerSettings.windowCenter,
          rescaleSlope: currentDicomFile.metadata.RescaleSlope,
          rescaleIntercept: currentDicomFile.metadata.RescaleIntercept,
          photometricInterpretation: currentDicomFile.metadata.PhotometricInterpretation,
        }
      );

      // Create cornerstone image object
      const cornerstoneImage = {
        imageId: currentDicomFile.id,
        minPixelValue: 0,
        maxPixelValue: 255,
        slope: 1,
        intercept: 0,
        windowCenter: currentDicomFile.metadata.WindowCenter || viewerSettings.windowCenter,
        windowWidth: currentDicomFile.metadata.WindowWidth || viewerSettings.windowWidth,
        render: cornerstone.renderGrayscaleImage,
        getPixelData: () => displayData,
        rows: currentDicomFile.height,
        columns: currentDicomFile.width,
        height: currentDicomFile.height,
        width: currentDicomFile.width,
        color: false,
        columnPixelSpacing: 1,
        rowPixelSpacing: 1,
        invert: false,
        sizeInBytes: displayData.length,
        data: currentDicomFile.metadata
      };

      const viewport = cornerstone.getDefaultViewportForImage(canvas, cornerstoneImage);
      viewport.voi.windowWidth = viewerSettings.windowWidth;
      viewport.voi.windowCenter = viewerSettings.windowCenter;
      viewport.scale = viewerSettings.zoom;
      viewport.rotation = viewerSettings.rotation;
      viewport.translation.x = viewerSettings.panX;
      viewport.translation.y = viewerSettings.panY;
      viewport.invert = viewerSettings.invert;

      cornerstone.displayImage(canvas, cornerstoneImage, viewport);

      setDicomMetadata(currentDicomFile.metadata);
    } catch (error) {
      console.error('Error rendering DICOM image:', error);
      setError('Failed to render DICOM image');
    }
  }, [currentDicomFile, viewerSettings]);

  useEffect(() => {
    renderDicomImage();
  }, [renderDicomImage]);

  const handleZoomIn = () => {
    setViewerSettings(prev => ({ ...prev, zoom: Math.min(prev.zoom * 1.2, 10) }));
  };

  const handleZoomOut = () => {
    setViewerSettings(prev => ({ ...prev, zoom: Math.max(prev.zoom / 1.2, 0.1) }));
  };

  const handleRotate = () => {
    setViewerSettings(prev => ({ ...prev, rotation: (prev.rotation + 90) % 360 }));
  };

  const handleReset = () => {
    setViewerSettings({
      zoom: 1,
      rotation: 0,
      panX: 0,
      panY: 0,
      windowWidth: 400,
      windowCenter: 40,
      invert: false
    });
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    setLastMousePosition({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;
    const deltaX = e.clientX - lastMousePosition.x;
    const deltaY = e.clientY - lastMousePosition.y;

    setViewerSettings(prev => ({
      ...prev,
      panX: prev.panX + deltaX,
      panY: prev.panY + deltaY
    }));

    setLastMousePosition({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleWheel = (e: React.WheelEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.deltaY < 0) {
      handleZoomIn();
    } else {
      handleZoomOut();
    }
  };

  if (loading) {
    return (
      <div className="h-screen bg-black flex items-center justify-center text-white">
        <div>Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-black flex items-center justify-center text-white">
        <div className="text-center">
          <p className="text-xl mb-4 text-red-400">Error: {error}</p>
          <button
            onClick={() => {
              setError(null);
              setLoading(false);
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (isUploading) {
    return (
      <div className="h-screen bg-black flex items-center justify-center text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-xl mb-4">Uploading DICOM files...</p>
          <p className="text-gray-400 mb-4">Processing {Math.round(loadingProgress)}% complete</p>

          <div className="w-64 mx-auto">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm">Progress</span>
              <span className="text-sm">{Math.round(loadingProgress)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (useDwvViewer) {
    return (
      <DwvDicomViewer
        onBack={() => setUseDwvViewer(false)}
        patientName={selectedPatient?.name}
        studyDescription={currentStudy?.studyDescription}
      />
    );
  }

  if (dicomStudies.length === 0) {
    return (
      <div className="h-screen bg-black flex items-center justify-center text-white">
        <div className="text-center">
          <p className="text-xl mb-4">No DICOM data found</p>
          <p className="text-gray-400 mb-4">Please upload DICOM files.</p>

          <div className="mb-6">
            <div className="relative inline-block">
              <input
                type="file"
                multiple
                {...({ webkitdirectory: '' } as any)}
                accept=".dcm,.dicom,application/dicom"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                disabled={isUploading}
                title="Upload DICOM directory"
                aria-label="Upload DICOM directory"
              />
              <button
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                disabled={isUploading}
              >
                <Upload className="w-5 h-5" />
                <span>Upload DICOM Directory</span>
              </button>
            </div>
          </div>

          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center space-x-2 mx-auto"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-black flex text-white">
      {/* Left Sidebar */}
      <div className="w-80 bg-gray-900 border-r border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigate('/')}
              className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back</span>
            </button>
            <button
              onClick={() => setUseDwvViewer(true)}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
            >
              DWV Viewer
            </button>
          </div>

          <div className="text-sm">
              <div className="font-medium">{selectedPatient?.name || 'Unknown Patient'}</div>
              <div className="text-gray-400">{currentStudy?.studyDescription || 'Unknown Study'}</div>
            </div>
        </div>

        {/* Studies List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <h3 className="text-sm font-medium mb-3 text-gray-300">Studies</h3>
            <div className="space-y-2">
              {dicomStudies.map((study) => (
                <div
                  key={study.studyInstanceUID}
                  onClick={() => handleStudySelect(study)}
                  className={`p-3 rounded cursor-pointer transition-colors ${
                    currentStudy?.studyInstanceUID === study.studyInstanceUID
                      ? 'bg-blue-600'
                      : 'bg-gray-800 hover:bg-gray-700'
                  }`}
                >
                  <div className="text-sm font-medium">{study.studyDescription}</div>
                  <div className="text-xs text-gray-400">{study.studyDate}</div>
                  <div className="text-xs text-gray-400">{study.series.length} series</div>
                </div>
              ))}
            </div>
          </div>

          {/* Series List */}
          {currentStudy && (
            <div className="p-4 border-t border-gray-700">
              <h3 className="text-sm font-medium mb-3 text-gray-300">Series</h3>
              <div className="space-y-2">
                {currentStudy.series.map((series) => (
                  <div
                    key={series.seriesInstanceUID}
                    onClick={() => handleSeriesSelect(series)}
                    className={`p-2 rounded cursor-pointer transition-colors ${
                      currentSeries?.seriesInstanceUID === series.seriesInstanceUID
                        ? 'bg-blue-600'
                        : 'bg-gray-800 hover:bg-gray-700'
                    }`}
                  >
                    <div className="text-sm">{series.seriesDescription}</div>
                    <div className="text-xs text-gray-400">{series.files.length} images</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Images List */}
          {currentSeries && (
            <div className="p-4 border-t border-gray-700">
              <h3 className="text-sm font-medium mb-3 text-gray-300">Images</h3>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {currentSeries.files.map((file, index) => (
                  <div
                    key={file.fileName}
                    onClick={() => handleImageSelect(file, index)}
                    className={`p-2 rounded cursor-pointer transition-colors text-xs ${
                      currentImageIndex === index
                        ? 'bg-blue-600'
                        : 'bg-gray-800 hover:bg-gray-700'
                    }`}
                  >
                    {index + 1}. {file.fileName}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-gray-900 p-4 border-b border-gray-700 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handlePreviousImage}
              disabled={!currentSeries || currentSeries.files.length <= 1}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Previous Image"
              aria-label="Previous Image"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <span className="text-sm">
              {currentImageIndex + 1} / {currentSeries?.files.length || 0}
            </span>
            <button
              onClick={handleNextImage}
              disabled={!currentSeries || currentSeries.files.length <= 1}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Next Image"
              aria-label="Next Image"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleZoomOut}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
              title="Zoom Out"
              aria-label="Zoom Out"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <button
              onClick={handleZoomIn}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
              title="Zoom In"
              aria-label="Zoom In"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
            <button
              onClick={handleRotate}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
              title="Rotate Image"
              aria-label="Rotate Image"
            >
              <RotateCw className="w-4 h-4" />
            </button>
            <button
              onClick={handleReset}
              className="px-3 py-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors text-sm"
              title="Reset View"
              aria-label="Reset View"
            >
              Reset
            </button>
          </div>
        </div>

        {/* Main Image Display */}
        <div className="flex-1 relative overflow-hidden">
          <div
            className="w-full h-full flex items-center justify-center cursor-move"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onWheel={handleWheel}
          >
            <canvas
              ref={canvasRef}
              className="max-w-none select-none border border-gray-600"
              style={{
                backgroundColor: '#1f2937',
                transform: `scale(${viewerSettings.zoom}) rotate(${viewerSettings.rotation}deg) translate(${viewerSettings.panX}px, ${viewerSettings.panY}px)`,
                transition: isDragging ? 'none' : 'transform 0.1s ease-out',
              }}
            />
          </div>

          {/* Image Info Overlay */}
          <div className="absolute top-4 left-4 bg-black bg-opacity-50 p-3 rounded text-sm max-w-xs">
            <div>Patient: {dicomMetadata?.patientName || 'Unknown'}</div>
            <div>Study: {dicomMetadata?.studyDescription || 'Unknown'}</div>
            <div>Date: {dicomMetadata?.studyDate || 'N/A'}</div>
            <div>ID: {dicomMetadata?.patientId || 'N/A'}</div>
            <div>File: {currentDicomFile?.fileName || 'N/A'}</div>
            {dicomMetadata?.imageSize && <div>Size: {dicomMetadata.imageSize}</div>}
            {dicomMetadata?.modality && <div>Modality: {dicomMetadata.modality}</div>}
          </div>

          {/* Window Level Info */}
          <div className="absolute top-4 right-4 bg-black bg-opacity-50 p-3 rounded text-sm">
            <div>WW: {viewerSettings.windowWidth}</div>
            <div>WC: {viewerSettings.windowCenter}</div>
            <div>Zoom: {Math.round(viewerSettings.zoom * 100)}%</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DicomViewer;