import React, { useEffect, useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { FileText, Folder, Image } from 'lucide-react';

interface DicomFileInfo {
  name: string;
  path: string;
  size: number;
  isDirectory: boolean;
}

const DicomFileLoader: React.FC<{ onFileSelect: (filePath: string) => void }> = ({ onFileSelect }) => {
  const [dicomFiles, setDicomFiles] = useState<DicomFileInfo[]>([]);
  const [currentPath, setCurrentPath] = useState('/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 模拟文件系统结构（基于之前的目录扫描结果）
  const mockFileSystem = {
    '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom': [
      { name: 'PCT001191', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/PCT001191', size: 0, isDirectory: true },
      { name: '肋骨', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨', size: 0, isDirectory: true }
    ],
    '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/PCT001191': [
      { name: 'CT', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/PCT001191/CT', size: 0, isDirectory: true },
      { name: 'PT', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/PCT001191/PT', size: 0, isDirectory: true }
    ],
    '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨': [
      // 模拟一些DICOM文件
      { name: '1.2.840.113619.2.55.3.514611216.984.**********.520.1.dcm', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨/1.2.840.113619.2.55.3.514611216.984.**********.520.1.dcm', size: 524288, isDirectory: false },
      { name: '1.2.840.113619.2.55.3.514611216.984.**********.520.2.dcm', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨/1.2.840.113619.2.55.3.514611216.984.**********.520.2.dcm', size: 524288, isDirectory: false },
      { name: '1.2.840.113619.2.55.3.514611216.984.**********.520.3.dcm', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨/1.2.840.113619.2.55.3.514611216.984.**********.520.3.dcm', size: 524288, isDirectory: false },
      { name: '1.2.840.113619.2.55.3.514611216.984.**********.520.4.dcm', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨/1.2.840.113619.2.55.3.514611216.984.**********.520.4.dcm', size: 524288, isDirectory: false },
      { name: '1.2.840.113619.2.55.3.514611216.984.**********.520.5.dcm', path: '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom/肋骨/1.2.840.113619.2.55.3.514611216.984.**********.520.5.dcm', size: 524288, isDirectory: false }
    ]
  };

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath]);

  const loadDirectory = (path: string) => {
    setLoading(true);
    setError(null);

    try {
      const files = mockFileSystem[path as keyof typeof mockFileSystem] || [];
      setDicomFiles(files);
    } catch (err) {
      setError('加载目录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleItemClick = (item: DicomFileInfo) => {
    console.log('handleItemClick called with:', item);
    if (item.isDirectory) {
      console.log('Navigating to directory:', item.path);
      setCurrentPath(item.path);
    } else {
      console.log('Selecting file:', item.path);
      onFileSelect(item.path);
    }
  };

  const goBack = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/');
    if (parentPath && parentPath !== '/Users/<USER>/git_project_vscode/09_medical/demo-web') {
      setCurrentPath(parentPath);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '-';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <Folder className="w-5 h-5 mr-2" />
          DICOM文件浏览器
        </CardTitle>
        <div className="text-sm text-gray-500">
          当前路径: {currentPath.replace('/Users/<USER>/git_project_vscode/09_medical/demo-web/', '')}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {currentPath !== '/Users/<USER>/git_project_vscode/09_medical/demo-web/dicom' && (
            <Button
              variant="outline"
              size="sm"
              onClick={goBack}
              className="mb-4"
            >
              ← 返回上级目录
            </Button>
          )}

          {loading && (
            <div className="text-center py-4">
              <div className="text-gray-500">加载中...</div>
            </div>
          )}

          {error && (
            <div className="text-center py-4">
              <div className="text-red-500">{error}</div>
            </div>
          )}

          {!loading && !error && (
            <div className="space-y-1 max-h-96 overflow-y-auto">
              {dicomFiles.map((file, index) => (
                <div
                  key={index}
                  onClick={() => {
                    console.log('Div clicked for:', file.name);
                    handleItemClick(file);
                  }}
                  className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <div className="flex-shrink-0 mr-3">
                    {file.isDirectory ? (
                      <Folder className="w-5 h-5 text-blue-500" />
                    ) : (
                      <Image className="w-5 h-5 text-green-500" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {file.isDirectory ? '目录' : `DICOM文件 · ${formatFileSize(file.size)}`}
                    </div>
                  </div>

                  {!file.isDirectory && (
                    <div className="flex-shrink-0">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onFileSelect(file.path);
                        }}
                      >
                        加载
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {!loading && !error && dicomFiles.length === 0 && (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <div className="text-gray-500">此目录为空</div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DicomFileLoader;