import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, User, Calendar, FileText, Save, FileType, RotateCcw, Eye, ExternalLink } from 'lucide-react';
import { useRadiologyStore } from '../store/useRadiologyStore';
import { toast } from 'sonner';
import { Patient } from '../types/radiology';
import { createMockDicomData } from '../utils/dicomUtils';

// PatientPanel 组件
const PatientPanel: React.FC = () => {
  const { patients, selectedPatient, setSelectedPatient } = useRadiologyStore();
  const [searchTerm, setSearchTerm] = useState('');

  // 过滤患者列表
  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.examType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* 标题栏 */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">患者列表</h2>
        
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="搜索患者姓名或检查类型"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>
      </div>

      {/* 患者列表 */}
      <div className="flex-1 overflow-y-auto">
        {filteredPatients.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <User className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>未找到匹配的患者</p>
          </div>
        ) : (
          <div className="p-2">
            {filteredPatients.map((patient) => (
              <div
                key={patient.id}
                onClick={() => handlePatientSelect(patient)}
                className={`p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                  selectedPatient?.id === patient.id
                    ? 'bg-blue-50 border-2 border-blue-200 shadow-sm'
                    : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                {/* 患者基本信息 */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <User className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-800">{patient.name}</h3>
                      <p className="text-sm text-gray-500">
                        {patient.gender} · {patient.age}岁
                      </p>
                    </div>
                  </div>
                  {selectedPatient?.id === patient.id && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>

                {/* 检查信息 */}
                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-600">
                    <FileText className="w-4 h-4 mr-2" />
                    <span>{patient.examType}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>{formatDate(patient.examDate)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <FileText className="w-4 h-4 mr-2" />
                    <span>{patient.department}</span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    <span className="text-xs text-gray-500">主诉:</span>
                    <span className="ml-1">{patient.chiefComplaint}</span>
                  </div>
                </div>

                {/* 检查ID */}
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500 font-mono">
                    ID: {patient.studyId}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部统计信息 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-sm text-gray-600">
          <span>共 {filteredPatients.length} 位患者</span>
          {searchTerm && (
            <span className="ml-2 text-blue-600">
              (搜索: "{searchTerm}")
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// ReportPanel 组件
const ReportPanel: React.FC = () => {
  const navigate = useNavigate();
  const {
    selectedPatient,
    currentReport,
    updateReport,
    saveReport,
    templates,
    selectedTemplate,
    setSelectedTemplate,
    applyTemplate,
    isReportModified
  } = useRadiologyStore();
  
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  // 处理报告内容更新
  const handleReportUpdate = (field: 'findings' | 'diagnosis' | 'suggestion' | 'criticalValue' | 'hpTest', value: string) => {
    if (!currentReport) return;
    updateReport(currentReport.id, { [field]: value });
  };
  
  // 打开DICOM查看器
  const handleOpenDicomViewer = () => {
    if (!selectedPatient) return;
    navigate(`/dicom-viewer?patientId=${selectedPatient.id}`);
  };

  // 保存报告
  const handleSaveReport = () => {
    if (!currentReport) return;
    saveReport();
    toast.success('报告已保存');
  };

  // 应用模板
  const handleApplyTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;
    
    setSelectedTemplate(template);
    applyTemplate();
    setShowTemplateSelector(false);
    toast.success(`已应用模板: ${template.name}`);
  };

  // 重置报告内容
  const handleResetReport = () => {
    if (!currentReport) return;
    updateReport(currentReport.id, {
      findings: '',
      diagnosis: '',
      suggestion: '',
      template: ''
    });
    toast.success('报告内容已重置');
  };

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取状态显示文本和颜色
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'draft':
        return { text: '草稿', color: 'text-yellow-600 bg-yellow-100' };
      case 'completed':
        return { text: '已完成', color: 'text-green-600 bg-green-100' };
      case 'reviewed':
        return { text: '已审核', color: 'text-blue-600 bg-blue-100' };
      default:
        return { text: '未知', color: 'text-gray-600 bg-gray-100' };
    }
  };

  if (!selectedPatient) {
    return (
      <div className="flex-1 bg-white border-l border-gray-200 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg mb-2">请选择患者</p>
          <p className="text-sm">选择患者后开始编写报告</p>
        </div>
      </div>
    );
  }

  if (!currentReport) {
    return (
      <div className="flex-1 bg-white border-l border-gray-200 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg mb-2">加载报告中...</p>
        </div>
      </div>
    );
  }

  const statusDisplay = getStatusDisplay(currentReport.status);

  return (
    <div className="flex-1 bg-white border-l border-gray-200 flex flex-col h-full">
      {/* 标题栏 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-800">诊断报告</h2>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.color}`}>
              {statusDisplay.text}
            </span>
            {isReportModified && (
              <span className="w-2 h-2 bg-orange-500 rounded-full" title="未保存的更改"></span>
            )}
          </div>
        </div>
        
        {/* 患者信息 */}
        <div className="bg-gray-50 p-2 rounded-lg">
          <div className="grid grid-cols-6 gap-2 text-sm text-gray-600">
            <div><span className="font-medium text-gray-800">{selectedPatient.name}</span></div>
            <div>{selectedPatient.gender} | {selectedPatient.age}岁</div>
            <div>{selectedPatient.examType}</div>
            <div>{selectedPatient.examDate}</div>
            <div className="col-span-2">{selectedPatient.chiefComplaint}</div>
          </div>
          <div className="font-mono text-xs text-gray-500 mt-1">{selectedPatient.studyId}</div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setShowTemplateSelector(!showTemplateSelector)}
            className="flex items-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm"
          >
            <FileType className="w-4 h-4 mr-1" />
            模板
          </button>
          
          <button
            onClick={handleResetReport}
            className="flex items-center px-3 py-2 bg-gray-50 text-gray-600 rounded-lg hover:bg-gray-100 transition-colors text-sm"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            重置
          </button>
          
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`flex items-center px-3 py-2 rounded-lg transition-colors text-sm ${
              previewMode 
                ? 'bg-green-50 text-green-600 hover:bg-green-100'
                : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
            }`}
          >
            <Eye className="w-4 h-4 mr-1" />
            {previewMode ? '编辑' : '预览'}
          </button>
          
          <button
            onClick={handleOpenDicomViewer}
            className="flex items-center px-3 py-2 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors text-sm"
          >
            <ExternalLink className="w-4 h-4 mr-1" />
            查看影像(旧版)
          </button>
          
          <button
            onClick={() => navigate('/modern-dicom-viewer')}
            className="flex items-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm"
          >
            <ExternalLink className="w-4 h-4 mr-1" />
            现代化查看器
          </button>
        </div>
        
        {/* 模板选择器 */}
        {showTemplateSelector && (
          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">选择模板</h4>
            <div className="space-y-1">
              {templates
                .filter(template => template.examType === selectedPatient.examType)
                .map(template => (
                  <button
                    key={template.id}
                    onClick={() => handleApplyTemplate(template.id)}
                    className="w-full text-left px-3 py-2 text-sm bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                  >
                    {template.name}
                  </button>
                ))
              }
            </div>
          </div>
        )}
      </div>

      {/* 报告内容 */}
      <div className="flex-1 p-4 min-h-0">
        {previewMode ? (
          /* 预览模式 */
          <div className="h-full flex flex-col space-y-3">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">检查所见</h4>
              <div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 whitespace-pre-wrap">
                {currentReport.findings || '暂无内容'}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">诊断意见</h4>
              <div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 whitespace-pre-wrap">
                {currentReport.diagnosis || '暂无内容'}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">建议</h4>
              <div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 whitespace-pre-wrap">
                {currentReport.suggestion || '暂无内容'}
              </div>
            </div>
            
            {/* 危机值和HP检测 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">危机值</h4>
                <div className={`bg-gray-50 p-3 rounded-lg text-sm font-medium ${
                  currentReport.criticalValue === '上报' ? 'text-red-600' : 'text-green-600'
                }`}>
                  {currentReport.criticalValue || '不上报'}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-800 mb-2">HP检测</h4>
                <div className={`bg-gray-50 p-3 rounded-lg text-sm font-medium ${
                  currentReport.hpTest === '阳性' ? 'text-red-600' : 'text-green-600'
                }`}>
                  {currentReport.hpTest || '阴性'}
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* 编辑模式 */
          <div className="h-full flex flex-col space-y-3">
            {/* 检查所见 */}
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                检查所见 *
              </label>
              <textarea
                value={currentReport.findings}
                onChange={(e) => handleReportUpdate('findings', e.target.value)}
                placeholder="请描述影像检查所见..."
                className="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
              />
            </div>
            
            {/* 诊断意见 */}
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                诊断意见 *
              </label>
              <textarea
                value={currentReport.diagnosis}
                onChange={(e) => handleReportUpdate('diagnosis', e.target.value)}
                placeholder="请输入诊断意见..."
                className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
              />
            </div>
            
            {/* 建议 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                建议
              </label>
              <textarea
                value={currentReport.suggestion}
                onChange={(e) => handleReportUpdate('suggestion', e.target.value)}
                placeholder="请输入建议..."
                className="w-full h-20 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
              />
            </div>
            
            {/* 危机值和HP检测 */}
            <div className="grid grid-cols-2 gap-3">
              {/* 危机值 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  危机值
                </label>
                <select
                  value={currentReport.criticalValue || '不上报'}
                  onChange={(e) => handleReportUpdate('criticalValue', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                >
                  <option value="不上报">不上报</option>
                  <option value="上报">上报</option>
                </select>
              </div>
              
              {/* HP检测 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  HP检测
                </label>
                <select
                  value={currentReport.hpTest || '阴性'}
                  onChange={(e) => handleReportUpdate('hpTest', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                >
                  <option value="阴性">阴性</option>
                  <option value="阳性">阳性</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 底部操作栏 */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <button
            onClick={handleSaveReport}
            disabled={!isReportModified}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              isReportModified
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <Save className="w-4 h-4 mr-2" />
            保存报告
          </button>
          
          {/* 报告信息 */}
          <div className="text-xs text-gray-500 text-right">
            <div>创建: {formatDateTime(currentReport.createdAt)}</div>
            <div>更新: {formatDateTime(currentReport.updatedAt)}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 主要的RadiologyWorkspace组件
const RadiologyWorkspace: React.FC = () => {
  const { setStudies } = useRadiologyStore();

  // 初始化DICOM数据
  useEffect(() => {
    const initializeDicomData = async () => {
      try {
        console.log('开始初始化DICOM数据...');
        const mockStudies = await createMockDicomData();
        setStudies(mockStudies);
        console.log('DICOM数据初始化完成:', mockStudies);
        
        if (mockStudies.length > 0) {
          toast.success(`已加载 ${mockStudies.length} 个DICOM研究`);
        }
      } catch (error) {
        console.error('初始化DICOM数据失败:', error);
        toast.error('初始化DICOM数据失败');
      }
    };

    initializeDicomData();
  }, [setStudies]);

  return (
    <div className="flex h-full bg-gray-100">
      <PatientPanel />
      <ReportPanel />
    </div>
  );
};

export default RadiologyWorkspace;