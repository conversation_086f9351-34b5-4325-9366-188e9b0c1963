import React, { useState, useEffect } from 'react';
import { Save, ChevronRight, Search, User, Calendar, FileText } from 'lucide-react';

interface PatientRecord {
  hospitalName: string;
  patientName: string;
  gender: string;
  age: string;
  patientId: string;
  department: string;
  visitDate: string;
  chiefComplaint: string;
  presentIllness: string;
  pastHistory: {
    surgeryHistory: string;
    allergyHistory: string;
    transfusionHistory: string;
    vaccinationHistory: string;
    infectiousHistory: string;
  };
  menstrualHistory: string;
  physicalExam: string;
  auxiliaryExam: string;
  diagnosis: string;
  secondaryDiagnosis?: string;
  labOrders?: string;
  examOrders?: string;
  treatment: string[];
  followUpNotes: string;
}

const samplePatients: PatientRecord[] = [
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '张明',
    gender: '男',
    age: '45',
    patientId: '13391478',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '突发胸痛伴呼吸困难2小时',
    presentIllness: '患者2小时前无明显诱因出现剧烈胸痛，疼痛位于胸骨后，呈压榨性，伴有大汗、呼吸困难、恶心，症状持续不缓解，遂急诊就医',
    pastHistory: {
      surgeryHistory: '胆囊切除术（2022年）',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '不适用',
    physicalExam: 'T 36.8℃，P 110次/分，R 25次/分，BP 165/95mmHg。神志清楚，痛苦貌，大汗淋漓。双肺呼吸音粗，未闻及明显啰音。心率110次/分，律不齐，可闻及第三心音。腹软，无压痛。双下肢无水肿。',
    auxiliaryExam: 'ECG：窦性心动过速，V1-V4导联ST段抬高>2mm；\n心肌酶谱：肌钙蛋白I 2.5ng/ml↑；\nBNP：560pg/ml↑；\n血常规：WBC 12.5×10^9/L↑',
    diagnosis: '急性前壁心肌梗死（发病2小时）',
    treatment: [
      '1. 立即给予吸氧、建立静脉通路',
      '2. 急诊冠状动脉造影+PCI手术',
      '3. 术前给予：',
      '   - 阿司匹林300mg嚼服',
      '   - 氯吡格雷600mg负荷量',
      '   - 肝素5000U静推',
      '4. 持续心电监护',
      '5. 建议立即住院治疗'
    ],
    followUpNotes: '已通知心导管室准备，家属已签署手术同意书'
  },
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '李强',
    gender: '男',
    age: '58',
    patientId: '13391481',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '车祸致左侧肢体疼痛、活动受限15分钟',
    presentIllness: '患者15分钟前驾驶电动车与机动车相撞，致左侧肢体疼痛，活动受限。现场目击者称事故后曾短暂意识丧失约1分钟。',
    pastHistory: {
      surgeryHistory: '无',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '不适用',
    physicalExam: 'T 36.7℃，P 95次/分，R 22次/分，BP 145/85mmHg。神志清楚，左额见3cm裂伤，左肩、左髋部压痛明显，左下肢活动受限，足背动脉搏动存在。',
    auxiliaryExam: '全身CT：左侧肱骨干骨折，左侧髋臼骨折，左侧胫腓骨骨折；\n头颅CT：未见明显异常；\n胸腹部CT：未见明显异常；\n血常规：正常范围',
    diagnosis: '多发性骨折（左肱骨、左髋臼、左胫腓骨）',
    treatment: [
      '1. 立即建立静脉通路',
      '2. acee：曲美布汀100mg静滴',
      '3. 患肢固定',
      '4. 创面处理',
      '5. 骨科会诊',
      '6. 建议立即住院手术'
    ],
    followUpNotes: '已通知骨科准备手术'
  },
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '王丽',
    gender: '女',
    age: '40',
    patientId: '13391482',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '持续性腹痛伴发热、呕吐12小时',
    presentIllness: '患者12小时前无明显诱因出现上腹部持续性疼痛，呈持续性，伴恶心呕吐3次，发热，体温最高38.5℃。既往体健。',
    pastHistory: {
      surgeryHistory: '无',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '月经规律',
    physicalExam: 'T 38.3℃，P 102次/分，R 20次/分，BP 125/75mmHg。神志清楚，上腹部压痛、反跳痛阳性，墨菲氏征阳性。',
    auxiliaryExam: '血常规：WBC 15.6×10^9/L↑，N 85%↑；\n血淀粉酶：正常；\n腹部B超：胆囊增大，壁厚，内见多发结石，周围见液性暗区；\n肝功能：转氨酶轻度升高',
    diagnosis: '急性胆囊炎',
    treatment: [
      '1. 禁食、胃肠减压',
      '2. 抗感染：头孢呋辛1.5g q8h 静滴',
      '3. 解痉止痛：654-2 50mg 肌注',
      '4. 补液：生理盐水500ml 静滴',
      '5. 普外科会诊',
      '6. 建议急诊手术'
    ],
    followUpNotes: '已通知普外科准备手术'
  },
  {
    hospitalName: '首都医科大学附属北京友谊医院(西城院区)',
    patientName: '赵华',
    gender: '女',
    age: '45',
    patientId: '13391483',
    department: '急诊科',
    visitDate: '2025-03-06',
    chiefComplaint: '突发左侧肢体活动障碍、言语不清2小时',
    presentIllness: '患者2小时前突发左侧肢体无力，不能站立行走，言语不清。既往有高血压病史5年，血压控制欠佳。',
    pastHistory: {
      surgeryHistory: '无',
      allergyHistory: '无',
      transfusionHistory: '无',
      vaccinationHistory: '已完成新冠疫苗接种',
      infectiousHistory: '无'
    },
    menstrualHistory: '绝经2年',
    physicalExam: 'T 36.5℃，P 88次/分，R 18次/分，BP 185/100mmHg。神志清楚，言语不清，左侧肢体肌力II级，病理征阳性。',
    auxiliaryExam: '头颅CT：右侧大脑中动脉区见片状低密度影；\nCT血管造影：右侧大脑中动脉M1段闭塞；\n心电图：窦性心律；\n血常规、凝血功能：正常范围',
    diagnosis: '急性缺血性脑卒中',
    treatment: [
      '1. 立即建立静脉通路',
      '2. 急诊静脉溶栓：',
      '   - rt-PA 0.9mg/kg',
      '   - 10%剂量静脉推注，余量持续泵入1小时',
      '3. 降压治疗：乌拉地尔10mg缓慢静推',
      '4. 神经内科会诊',
      '5. 建议立即住院'
    ],
    followUpNotes: '已收入卒中单元'
  }
];

export default function ClinicalWorkspace() {
  const [currentPatientIndex, setCurrentPatientIndex] = useState(0);
  const [sourceValue, setSourceValue] = useState('');

  const [record, setRecord] = useState<PatientRecord>(samplePatients[0]);

  useEffect(() => {
    const cookies = document.cookie.split('; ');
    const sourceCookie = cookies.find(cookie => cookie.startsWith('SOURCE='));
    if (sourceCookie) {
      const value = sourceCookie.split('=')[1];
      setSourceValue(decodeURIComponent(value));
    }
  }, []);

  const handleSourceValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSourceValue(value);
    document.cookie = `SOURCE=${encodeURIComponent(value)}; path=/; max-age=31536000`;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setRecord(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePastHistoryChange = (field: keyof typeof record.pastHistory, value: string) => {
    setRecord(prev => ({
      ...prev,
      pastHistory: {
        ...prev.pastHistory,
        [field]: value
      }
    }));
  };

  const handleSave = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const baseUrl = ((window as any).WATSON_API_BASE_URL) || "http://*************:27861/api/signals/v1/submit";

    const request = {
      source: '',
      name: record.patientName,
      gender: record.gender,
      age: record.age,
      patientId: record.patientId,
      department: record.department,
      visitDate: record.visitDate,
      complaints: record.chiefComplaint,
      abstractHistory: `${record.presentIllness} 手术史：${record.pastHistory.surgeryHistory}，过敏史：${record.pastHistory.allergyHistory}，输血史：${record.pastHistory.transfusionHistory}，预防接种史：${record.pastHistory.vaccinationHistory}，传染病史：${record.pastHistory.infectiousHistory}`,
      diagnosis: record.diagnosis,
    };

    const cookies = document.cookie.split('; ');
    const sourceCookie = cookies.find(cookie => cookie.startsWith('SOURCE='));
    if (sourceCookie) {
      const sourceValue = sourceCookie.split('=')[1];
      request.source = decodeURIComponent(sourceValue);
    }

    console.log('Sending patient data:', request);
  
    fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      alert('患者记录已成功保存！');
    })  
    .catch(error => {
      console.error('Error saving patient data:', error);
      alert('保存失败，请重试');
    });
  };

  const handleNext = () => {
    const nextIndex = (currentPatientIndex + 1) % samplePatients.length;
    setCurrentPatientIndex(nextIndex);
    setRecord(samplePatients[nextIndex]);
  };

  // 患者列表组件
  const PatientList = () => {
    const [searchTerm, setSearchTerm] = useState('');
    
    const filteredPatients = samplePatients.filter(patient =>
      patient.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.department.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    };

    return (
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
        {/* 标题栏 */}
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">患者列表</h2>
          
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="搜索患者姓名或科室"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
        </div>

        {/* 患者列表 */}
        <div className="flex-1 overflow-y-auto">
          {filteredPatients.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <User className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p>未找到匹配的患者</p>
            </div>
          ) : (
            <div className="p-2">
              {filteredPatients.map((patient, index) => (
                <div
                  key={patient.patientId}
                  onClick={() => {
                    setCurrentPatientIndex(index);
                    setRecord(patient);
                  }}
                  className={`p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                    currentPatientIndex === samplePatients.indexOf(patient)
                      ? 'bg-blue-50 border-2 border-blue-200 shadow-sm'
                      : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  {/* 患者基本信息 */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <User className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-800">{patient.patientName}</h3>
                        <p className="text-sm text-gray-500">
                          {patient.gender} · {patient.age}岁
                        </p>
                      </div>
                    </div>
                    {currentPatientIndex === samplePatients.indexOf(patient) && (
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    )}
                  </div>

                  {/* 就诊信息 */}
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <FileText className="w-4 h-4 mr-2" />
                      <span>{patient.department}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>{formatDate(patient.visitDate)}</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      <span className="text-xs text-gray-500">主诉:</span>
                      <span className="ml-1">{patient.chiefComplaint.substring(0, 30)}...</span>
                    </div>
                  </div>

                  {/* 患者ID */}
                  <div className="mt-2 pt-2 border-t border-gray-200">
                    <p className="text-xs text-gray-500 font-mono">
                      ID: {patient.patientId}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部统计信息 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            <span>共 {filteredPatients.length} 位患者</span>
            {searchTerm && (
              <span className="ml-2 text-green-600">
                (搜索: "{searchTerm}")
              </span>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 p-2 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-800">临床病历录入</h2>
          <div className="flex gap-2">
            <button 
              onClick={handleSave}
              className="flex items-center px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition text-sm"
            >
              <Save className="w-4 h-4 mr-1" />
              保存
            </button>
            <button 
              onClick={handleNext}
              className="flex items-center px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition text-sm"
            >
              <ChevronRight className="w-4 h-4 mr-1" />
              下一个
            </button>
            <div className="flex items-center gap-2 ml-4">
              <label className="text-sm font-medium text-gray-700">来源</label>
              <input
                type="text"
                name="sourceValue"
                value={sourceValue}
                onChange={handleSourceValue}
                className="w-40 h-8 rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm"
                placeholder="请输入数据来源"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 三栏布局主体 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 第1栏：患者列表 - 响应式宽度 */}
        <div className="w-80 min-w-64 max-w-96 bg-white border-r border-gray-200 flex flex-col resize-x overflow-hidden">
          <PatientList />
        </div>

        {/* 第2栏：患者基本信息、主诉、现病史、检查信息 - 整合左侧栏，响应式宽度 */}
        <div className="flex-1 min-w-80 max-w-2xl bg-white border-r border-gray-200 flex flex-col overflow-hidden resize-x">
          <div className="p-3 border-b border-gray-200 bg-gray-50">
            <h3 className="text-sm font-semibold text-gray-800">基本信息 & 检查信息</h3>
          </div>
          <div className="flex-1 overflow-y-auto p-3 space-y-3">
            {/* 患者基本信息 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">患者信息</div>
              <div className="p-3 space-y-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">姓名</span>
                    <input
                      type="text"
                      name="patientName"
                      value={record.patientName}
                      onChange={handleChange}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">性别</span>
                    <select
                      name="gender"
                      value={record.gender}
                      onChange={handleChange}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    >
                      <option value="男">男</option>
                      <option value="女">女</option>
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">年龄</span>
                    <input
                      type="text"
                      name="age"
                      value={record.age}
                      onChange={handleChange}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">ID号</span>
                    <input
                      type="text"
                      name="patientId"
                      value={record.patientId}
                      onChange={handleChange}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">科室</span>
                    <input
                      type="text"
                      name="department"
                      value={record.department}
                      onChange={handleChange}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">日期</span>
                    <input
                      type="date"
                      name="visitDate"
                      value={record.visitDate}
                      onChange={handleChange}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 主诉 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">主诉</div>
              <div className="p-3">
                <textarea
                  name="chiefComplaint"
                  value={record.chiefComplaint}
                  onChange={handleChange}
                  rows={3}
                  className="w-full border-none resize-none text-sm focus:outline-none"
                  placeholder="请输入主诉"
                />
              </div>
            </div>

            {/* 现病史 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">现病史</div>
              <div className="p-3">
                <textarea
                  name="presentIllness"
                  value={record.presentIllness}
                  onChange={handleChange}
                  rows={4}
                  className="w-full border-none resize-none text-sm focus:outline-none"
                  placeholder="请输入现病史"
                />
              </div>
            </div>

            {/* 既往史 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">既往史</div>
              <div className="p-3 space-y-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">手术</span>
                    <input
                      type="text"
                      name="surgeryHistory"
                      value={record.pastHistory.surgeryHistory}
                      onChange={(e) => setRecord(prev => ({
                        ...prev,
                        pastHistory: { ...prev.pastHistory, surgeryHistory: e.target.value }
                      }))}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">过敏</span>
                    <input
                      type="text"
                      name="allergyHistory"
                      value={record.pastHistory.allergyHistory}
                      onChange={(e) => setRecord(prev => ({
                        ...prev,
                        pastHistory: { ...prev.pastHistory, allergyHistory: e.target.value }
                      }))}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">输血</span>
                    <input
                      type="text"
                      name="transfusionHistory"
                      value={record.pastHistory.transfusionHistory}
                      onChange={(e) => setRecord(prev => ({
                        ...prev,
                        pastHistory: { ...prev.pastHistory, transfusionHistory: e.target.value }
                      }))}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">疫苗</span>
                    <input
                      type="text"
                      name="vaccinationHistory"
                      value={record.pastHistory.vaccinationHistory}
                      onChange={(e) => setRecord(prev => ({
                        ...prev,
                        pastHistory: { ...prev.pastHistory, vaccinationHistory: e.target.value }
                      }))}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex items-center min-w-0">
                    <span className="w-20 flex-shrink-0 text-gray-600 whitespace-nowrap">传染病</span>
                    <input
                      type="text"
                      name="infectiousHistory"
                      value={record.pastHistory.infectiousHistory}
                      onChange={(e) => setRecord(prev => ({
                        ...prev,
                        pastHistory: { ...prev.pastHistory, infectiousHistory: e.target.value }
                      }))}
                      className="flex-1 min-w-0 border-none bg-transparent text-sm focus:outline-none ml-2"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 查体 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">查体</div>
              <div className="p-3">
                <textarea
                  name="physicalExam"
                  value={record.physicalExam}
                  onChange={handleChange}
                  rows={3}
                  className="w-full border-none resize-none text-sm focus:outline-none"
                  placeholder="请输入查体结果"
                />
              </div>
            </div>

            {/* 辅助检查 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">辅助检查</div>
              <div className="p-3">
                <textarea
                  name="auxiliaryExam"
                  value={record.auxiliaryExam}
                  onChange={handleChange}
                  rows={3}
                  className="w-full border-none resize-none text-sm focus:outline-none"
                  placeholder="请输入辅助检查结果"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 第3栏：诊断信息和医嘱录入 - 响应式宽度 */}
        <div className="flex-1 min-w-80 max-w-2xl bg-white flex flex-col overflow-hidden">
          <div className="p-3 border-b border-gray-200 bg-gray-50">
            <h3 className="text-sm font-semibold text-gray-800">诊断信息 & 医嘱录入</h3>
          </div>
          <div className="flex-1 overflow-y-auto p-3 space-y-3">

            {/* 诊断区域 - 分离主诊断和次要诊断 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">诊断</div>
              <div className="p-3 space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">主诊断：</label>
                  <input
                    type="text"
                    name="diagnosis"
                    value={record.diagnosis}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:border-blue-500"
                    placeholder="请输入主诊断"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">次要诊断：</label>
                  <textarea
                    name="secondaryDiagnosis"
                    value={record.secondaryDiagnosis || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full border border-gray-300 rounded px-2 py-1 text-sm resize-none focus:outline-none focus:border-blue-500"
                    placeholder="请输入次要诊断（如有）"
                  />
                </div>
              </div>
            </div>

            {/* 医嘱录入 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">医嘱录入</div>
              <div className="p-3 space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">检验项目：</label>
                  <textarea
                    name="labOrders"
                    value={record.labOrders || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full border border-gray-300 rounded px-2 py-1 text-sm resize-none focus:outline-none focus:border-blue-500"
                    placeholder="请输入检验项目（如：血常规、肝功能、肾功能等）"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">检查项目：</label>
                  <textarea
                    name="examOrders"
                    value={record.examOrders || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full border border-gray-300 rounded px-2 py-1 text-sm resize-none focus:outline-none focus:border-blue-500"
                    placeholder="请输入检查项目（如：胸片、CT、B超等）"
                  />
                </div>
              </div>
            </div>

            {/* 治疗 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">治疗</div>
              <div className="p-3">
                <textarea
                  name="treatment"
                  value={record.treatment.join('\n')}
                  onChange={(e) => setRecord(prev => ({ ...prev, treatment: e.target.value.split('\n') }))}
                  rows={4}
                  className="w-full border-none resize-none text-sm focus:outline-none"
                  placeholder="请输入治疗方案"
                />
              </div>
            </div>

            {/* 随访 */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 p-2 text-sm font-medium text-gray-700">随访</div>
              <div className="p-3">
                <textarea
                  name="followUpNotes"
                  value={record.followUpNotes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full border-none resize-none text-sm focus:outline-none"
                  placeholder="请输入随访计划"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}