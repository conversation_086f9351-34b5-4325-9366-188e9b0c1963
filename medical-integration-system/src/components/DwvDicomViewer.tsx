import React, { useEffect, useRef, useState } from 'react';
import { ArrowLeft, Upload, Folder } from 'lucide-react';
import { getAvailableSequences, getSequenceUrls, DicomSequence } from '../utils/dicomSource';

// 定义DWV App的简化类型
interface DwvApp {
  init: (config: any) => void;
  loadURLs: (urls: string[]) => void;
  loadFiles: (files: File[]) => void;
  reset: () => void;
  addEventListener: (type: string, listener: (event?: any) => void) => void;
}

interface DwvDicomViewerProps {
  onBack?: () => void;
  patientName?: string;
  studyDescription?: string;
}

const DwvDicomViewer: React.FC<DwvDicomViewerProps> = ({
  onBack,
  patientName = '未知患者',
  studyDescription = '未知检查'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dwvApp, setDwvApp] = useState<DwvApp | null>(null);
  
  // 清单/序列相关状态
  const [availableSequences, setAvailableSequences] = useState<DicomSequence[]>([]);
  const [selectedSequence, setSelectedSequence] = useState<DicomSequence | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [showDirectoryPanel, setShowDirectoryPanel] = useState(false);
  const [sequenceUrls, setSequenceUrls] = useState<string[] | null>(null);

  useEffect(() => {
    const initDwv = async () => {
      try {
        console.log('开始初始化DWV...');
        
        // 动态导入DWV
        const { App: DwvApp } = await import('dwv');
        
        if (!containerRef.current) {
          console.error('容器引用不存在');
          return;
        }

        // 确保容器有ID
        if (!containerRef.current.id) {
          containerRef.current.id = 'dwv-container-' + Date.now();
        }

        const app: DwvApp = new DwvApp();
        
        console.log('DWV应用创建成功，开始初始化...');
        
        // 最简化的配置
        const config = {
          dataViewConfigs: {
            '*': [{
              divId: containerRef.current.id
            }]
          }
        };

        console.log('DWV配置:', config);
        
        // 初始化应用
        app.init(config);
        setDwvApp(app);
        
        console.log('DWV初始化成功');

        // 设置事件监听器
        app.addEventListener('loadstart', () => {
          console.log('开始加载DICOM文件...');
          setIsLoading(true);
          setError(null);
        });

        app.addEventListener('loadend', () => {
          console.log('DICOM文件加载完成');
          setIsLoading(false);
        });

        app.addEventListener('error', (event: { error?: string }) => {
          console.error('DWV错误:', event);
          setIsLoading(false);
          setError(`加载错误: ${event.error || '未知错误'}`);
        });

        app.addEventListener('loadprogress', (event: { loaded: number }) => {
          console.log('加载进度:', event.loaded);
        });

        // 预加载清单
        try {
          const seqs = await getAvailableSequences('/dicom');
          setAvailableSequences(seqs);
          setShowDirectoryPanel(true);
        } catch (e) {
          console.warn('加载序列清单失败:', e);
        }

      } catch (err) {
        console.error('DWV初始化失败:', err);
        setError(`DICOM查看器初始化失败: ${err instanceof Error ? err.message : '未知错误'}`);
      }
    };

    initDwv();

    return () => {
      if (dwvApp) {
        try {
          dwvApp.reset();
        } catch (err) {
          console.warn('DWV清理警告:', err);
        }
      }
    };
  }, []);

  // 取消预设文件加载，使用序列清单

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0 || !dwvApp) return;

    try {
      setError(null);
      console.log(`开始加载 ${files.length} 个文件`);
      
      // 加载文件
      dwvApp.loadFiles(Array.from(files));
    } catch (err) {
      console.error('文件加载失败:', err);
      setError(`文件加载失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  // 加载选中的序列（清单优先，失败则快速探测）
  const handleLoadSequence = async (seq: DicomSequence) => {
    if (!dwvApp) {
      setError('DWV查看器未初始化');
      return;
    }
    setIsLoading(true);
    setError(null);
    setSelectedSequence(seq);
    try {
      const urls = await getSequenceUrls('/dicom', seq);
      if (!urls.length) throw new Error('未发现该序列下的DICOM文件');
      setSequenceUrls(urls);
      dwvApp.reset();
      dwvApp.loadURLs(urls);
    } catch (e) {
      setError(e instanceof Error ? e.message : '加载序列失败');
      setIsLoading(false);
    }
  };

  // 切换目录面板显示
  const toggleDirectoryPanel = () => {
    setShowDirectoryPanel(!showDirectoryPanel);
  };

  // 预览与批量加载功能已移除，专注于单序列加载

  return (
    <div className="h-screen bg-black flex flex-col text-white">
      {/* 顶部工具栏 */}
      <div className="bg-gray-900 p-4 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center space-x-4">
          {onBack && (
            <button 
              onClick={onBack}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
              aria-label="返回"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回</span>
            </button>
          )}
          
          <div className="text-sm">
            <span className="font-medium">{patientName}</span>
            <span className="mx-2">·</span>
            <span>{studyDescription}</span>
            <span className="mx-2">·</span>
            <span className="text-blue-400">DWV专业查看器</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* 目录浏览按钮 */}
          <button
            onClick={toggleDirectoryPanel}
            className={`p-2 rounded transition-colors ${
              showDirectoryPanel ? 'bg-blue-600 hover:bg-blue-500' : 'bg-gray-600 hover:bg-gray-500'
            }`}
            title="浏览DICOM目录"
            aria-label="目录浏览"
          >
            <Folder className="w-4 h-4" />
          </button>

          {/* 文件上传 */}
          <div className="relative">
            <input
              type="file"
              multiple
              accept=".dcm,.dicom,application/dicom"
              onChange={handleFileUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={isLoading}
              title="上传DICOM文件"
              aria-label="上传DICOM文件"
            />
            <button 
              className="p-2 bg-blue-600 rounded hover:bg-blue-500 transition-colors disabled:opacity-50"
              title="上传DICOM文件"
              disabled={isLoading}
              aria-label="上传文件按钮"
            >
              <Upload className="w-4 h-4" />
            </button>
          </div>

          {/* 使用清单加载，不提供示例按钮 */}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex relative">
        {/* 目录面板 */}
        {showDirectoryPanel && (
          <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <Folder className="w-5 h-5 mr-2" />
                DICOM清单
              </h3>
              <div className="space-y-2">
                {availableSequences.map((seq) => (
                  <button
                    key={seq.id}
                    onClick={() => handleLoadSequence(seq)}
                    disabled={isScanning}
                    className={`w-full text-left p-2 rounded text-sm transition-colors ${
                      selectedSequence?.id === seq.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    }`}
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{seq.name || seq.id}</span>
                      <span className="text-xs opacity-70">{seq.path}</span>
                    </div>
                  </button>
                ))}
                {availableSequences.length === 0 && (
                  <div className="text-xs text-gray-400">未找到清单。请在 public/dicom 下创建 manifest.json。</div>
                )}
              </div>
            </div>

            {sequenceUrls && (
              <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                  <div className="text-sm text-gray-300 mb-2">文件数: {sequenceUrls.length}</div>
                  <div className="text-xs text-gray-400 space-y-1 max-h-40 overflow-y-auto">
                    {sequenceUrls.slice(0, 20).map((u, i) => (
                      <div key={i} className="truncate">{u.split('/').pop()}</div>
                    ))}
                    {sequenceUrls.length > 20 && <div>…</div>}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 预览面板已移除，直接加载序列 */}

        {/* DWV查看器区域 */}
        <div className="flex-1 relative">
        {/* 加载状态 */}
        {isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-xl">正在加载DICOM文件...</p>
            </div>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="absolute top-4 left-4 right-4 bg-red-600 text-white p-4 rounded z-10">
            <div className="flex items-center space-x-2">
              <span className="font-medium">错误:</span>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* DWV容器 */}
        <div 
          ref={containerRef}
          id="dwv-main-container"
          className="w-full h-full bg-gray-900"
          style={{ 
            minHeight: '500px',
            position: 'relative'
          }}
        >
          {/* 如果没有加载图像，显示提示 */}
          {!isLoading && !error && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              <div className="text-center">
                <Upload className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-xl mb-2">DWV专业DICOM查看器</p>
                <p className="text-sm mb-4">请上传DICOM文件，或从左侧清单选择序列</p>
                <p className="text-xs text-gray-500">支持 .dcm 和 .dicom 格式</p>
              </div>
            </div>
          )}
        </div>

        {/* 状态信息 */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-75 p-3 rounded text-xs text-gray-300 max-w-xs">
          <div>DWV状态: {dwvApp ? '已初始化' : '初始化中...'}</div>
          <div>容器ID: {containerRef.current?.id || '未设置'}</div>
          <div>加载状态: {isLoading ? '加载中' : '就绪'}</div>
          {selectedSequence && (
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div>当前序列: {selectedSequence.name || selectedSequence.id}</div>
              {sequenceUrls && <div>文件数: {sequenceUrls.length}</div>}
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
};

export default DwvDicomViewer;
