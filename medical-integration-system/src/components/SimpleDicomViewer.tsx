import React, { useEffect, useRef, useState } from 'react';
import { 
  RenderingEngine, 
  Enums, 
  init,
  imageLoader,
  metaData,
  cache
} from '@cornerstonejs/core';
import { 
  addTool, 
  ToolGroupManager, 
  WindowLevelTool, 
  PanTool, 
  ZoomTool, 
  StackScrollTool,
  Enums as ToolEnums
} from '@cornerstonejs/tools';
import cornerstoneDICOMImageLoader from '@cornerstonejs/dicom-image-loader';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Upload, ZoomIn, ZoomOut, RotateCw, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';
import DicomFileLoader from './DicomFileLoader';

// 简化的初始化，不使用DICOM image loader
let isCornerstone3DInitialized = false;

const initializeCornerstone3D = async () => {
  if (isCornerstone3DInitialized) {
    console.log('Cornerstone3D already initialized, skipping...');
    return;
  }
  
  try {
    // 简化的初始化配置
    await init({
      rendering: {
        preferSizeOverAccuracy: false,
        strictZSpacingForVolumeViewport: true,
      },
    });

    // 配置DICOM图像加载器
    cornerstoneDICOMImageLoader.external.cornerstone = {
      ...cornerstoneDICOMImageLoader.external.cornerstone,
    };
    cornerstoneDICOMImageLoader.external.dicomParser = require('dicom-parser');
    cornerstoneDICOMImageLoader.configure({
      useWebWorkers: true,
      decodeConfig: {
        convertFloatPixelDataToInt: false,
      },
    });

    // 注册图像加载器
    imageLoader.registerImageLoader('wadouri', cornerstoneDICOMImageLoader.wadouri.loadImage);
    imageLoader.registerImageLoader('wadors', cornerstoneDICOMImageLoader.wadors.loadImage);
    
    // 注册mock图像加载器
    imageLoader.registerImageLoader('mock', (imageId: string) => {
      const cache = (window as any).cornerstoneImageCache;
      if (cache && cache.has(imageId)) {
        return cache.get(imageId);
      }
      
      // 如果缓存中没有，返回一个空的promise
      return {
        promise: Promise.reject(new Error('Image not found in cache')),
        cancelFn: undefined,
        decache: () => {}
      };
    });
    
    // Add tools
    addTool(WindowLevelTool);
    addTool(PanTool);
    addTool(ZoomTool);
    addTool(StackScrollTool);
    
    isCornerstone3DInitialized = true;
    console.log('Cornerstone3D initialized successfully with DICOM loader');
  } catch (error) {
    console.error('Failed to initialize Cornerstone3D:', error);
  }
};

interface DicomFile {
  file: File;
  imageId: string;
  metadata?: any;
}

interface DicomSeries {
  seriesInstanceUID: string;
  files: DicomFile[];
  description?: string;
}

const SimpleDicomViewer: React.FC = () => {
  const viewportRef = useRef<HTMLDivElement>(null);
  const [renderingEngine, setRenderingEngine] = useState<RenderingEngine | null>(null);
  const [dicomSeries, setDicomSeries] = useState<DicomSeries[]>([]);
  const [currentSeriesIndex, setCurrentSeriesIndex] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [totalImages, setTotalImages] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewportInfo, setViewportInfo] = useState({
    zoom: 1,
    windowWidth: 0,
    windowCenter: 0
  });
  const [activeTab, setActiveTab] = useState<'upload' | 'browse'>('browse');

  const viewportId = 'CT_AXIAL_STACK';
  const renderingEngineId = 'myRenderingEngine';
  const toolGroupId = 'STACK_TOOL_GROUP_ID';

  // 初始化Cornerstone3D
  useEffect(() => {
    initializeCornerstone3D();
  }, []);

  // 创建渲染引擎
  useEffect(() => {
    if (!isCornerstone3DInitialized || !viewportRef.current) return;

    const setupRenderingEngine = async () => {
      try {
        const engine = new RenderingEngine(renderingEngineId);
        
        const viewportInput = {
          viewportId,
          type: Enums.ViewportType.STACK,
          element: viewportRef.current!,
          defaultOptions: {
            background: [0.2, 0.3, 0.4] as [number, number, number],
          },
        };

        engine.enableElement(viewportInput);
        setRenderingEngine(engine);

        // 设置工具组
        const toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
        if (toolGroup) {
          toolGroup.addTool(WindowLevelTool.toolName);
          toolGroup.addTool(PanTool.toolName);
          toolGroup.addTool(ZoomTool.toolName);
          toolGroup.addTool(StackScrollTool.toolName);

          toolGroup.setToolActive(WindowLevelTool.toolName, {
            bindings: [{ mouseButton: ToolEnums.MouseBindings.Primary }],
          });
          toolGroup.setToolActive(PanTool.toolName, {
            bindings: [{ mouseButton: ToolEnums.MouseBindings.Auxiliary }],
          });
          toolGroup.setToolActive(ZoomTool.toolName, {
            bindings: [{ mouseButton: ToolEnums.MouseBindings.Secondary }],
          });
          toolGroup.setToolActive(StackScrollTool.toolName);

          toolGroup.addViewport(viewportId, renderingEngineId);
        }
      } catch (error) {
        console.error('Failed to setup rendering engine:', error);
        setError('Failed to initialize viewer');
      }
    };

    setupRenderingEngine();

    return () => {
      if (renderingEngine) {
        renderingEngine.destroy();
      }
    };
  }, [isCornerstone3DInitialized]);

  // 加载DICOM文件（简化版本）
  const loadDicomFile = async (filePath: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('Loading DICOM file from:', filePath);
      
      if (!renderingEngine) {
        throw new Error('Rendering engine not initialized');
      }

      // 创建模拟的图像数据（512x512灰度图像）
      const width = 512;
      const height = 512;
      const pixelData = new Uint16Array(width * height);
      
      // 生成一个简单的测试图案
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const index = y * width + x;
          // 创建一个简单的渐变图案
          const value = Math.floor((x + y) / 2) % 65536;
          pixelData[index] = value;
        }
      }

      // 创建图像对象
      const imageId = `mock:${filePath}`;
      const image = {
        imageId,
        minPixelValue: 0,
        maxPixelValue: 65535,
        slope: 1,
        intercept: 0,
        windowCenter: 32768,
        windowWidth: 65536,
        getPixelData: () => pixelData,
        rows: height,
        columns: width,
        height,
        width,
        color: false,
        rgba: false,
        numComps: 1,
        columnPixelSpacing: 1,
        rowPixelSpacing: 1,
        invert: false,
        sizeInBytes: pixelData.byteLength
      };

      // 注册图像到缓存（在设置viewport之前）
      const imageLoadObject = {
        promise: Promise.resolve(image),
        cancelFn: undefined,
        decache: () => {}
      };
      
      // 手动注册图像
      (window as any).cornerstoneImageCache = (window as any).cornerstoneImageCache || new Map();
      (window as any).cornerstoneImageCache.set(imageId, imageLoadObject);

      // 获取viewport并设置图像
      const viewport = renderingEngine.getViewport(viewportId);
      if (viewport) {
        try {
          // 对于Stack viewport，使用setImageIds方法
          const stackViewport = viewport as any;
          if (stackViewport.setImageIds) {
            await stackViewport.setImageIds([imageId]);
            stackViewport.render();
            console.log('DICOM file loaded successfully:', filePath);
            setCurrentImageIndex(0);
            setTotalImages(1);
          } else {
            throw new Error('Unable to set image on viewport - setImageIds not available');
          }
        } catch (stackError) {
          console.error('Error setting image:', stackError);
          throw stackError;
        }
      } else {
        throw new Error('Viewport not found');
      }

    } catch (error) {
      console.error('Error loading DICOM file:', error);
      setError(`Failed to load DICOM file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = (files: DicomSeries[]) => {
    setDicomSeries(files);
    setCurrentSeriesIndex(0);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    if (dicomSeries.length === 0) return;
    const currentSeries = dicomSeries[currentSeriesIndex];
    if (currentImageIndex < currentSeries.files.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
  };

  const previousImage = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    }
  };

  const resetView = () => {
    if (renderingEngine) {
      const viewport = renderingEngine.getViewport(viewportId);
      if (viewport) {
        viewport.resetCamera();
        viewport.render();
      }
    }
  };

  const zoomIn = () => {
    if (renderingEngine) {
      const viewport = renderingEngine.getViewport(viewportId);
      if (viewport) {
        const camera = viewport.getCamera();
        camera.parallelScale *= 0.8;
        viewport.setCamera(camera);
        viewport.render();
      }
    }
  };

  const zoomOut = () => {
    if (renderingEngine) {
      const viewport = renderingEngine.getViewport(viewportId);
      if (viewport) {
        const camera = viewport.getCamera();
        camera.parallelScale *= 1.2;
        viewport.setCamera(camera);
        viewport.render();
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">简化DICOM查看器</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧控制面板 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 文件上传/浏览选项卡 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  DICOM文件
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  <button
                    onClick={() => setActiveTab('browse')}
                    className={`flex-1 py-2 px-4 text-sm font-medium rounded-l-md border ${
                      activeTab === 'browse'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    浏览文件
                  </button>
                  <button
                    onClick={() => setActiveTab('upload')}
                    className={`flex-1 py-2 px-4 text-sm font-medium rounded-r-md border-t border-r border-b ${
                      activeTab === 'upload'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    上传文件
                  </button>
                </div>

                {activeTab === 'browse' && (
                  <DicomFileLoader onFileSelect={loadDicomFile} />
                )}

                {activeTab === 'upload' && (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-600">
                      拖拽DICOM文件到这里或点击上传
                    </p>
                    <input
                      type="file"
                      multiple
                      accept=".dcm,.dicom"
                      className="hidden"
                      aria-label="选择DICOM文件"
                      onChange={(e) => {
                        // 处理文件上传
                        console.log('Files selected:', e.target.files);
                      }}
                    />
                    <Button className="mt-2" variant="outline">
                      选择文件
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 图像控制 */}
            <Card>
              <CardHeader>
                <CardTitle>图像控制</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <Button onClick={zoomIn} variant="outline" size="sm">
                    <ZoomIn className="w-4 h-4 mr-1" />
                    放大
                  </Button>
                  <Button onClick={zoomOut} variant="outline" size="sm">
                    <ZoomOut className="w-4 h-4 mr-1" />
                    缩小
                  </Button>
                  <Button onClick={resetView} variant="outline" size="sm">
                    <RefreshCw className="w-4 h-4 mr-1" />
                    重置
                  </Button>
                  <Button variant="outline" size="sm">
                    <RotateCw className="w-4 h-4 mr-1" />
                    旋转
                  </Button>
                </div>

                {dicomSeries.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Button onClick={previousImage} variant="outline" size="sm">
                        <ChevronLeft className="w-4 h-4" />
                      </Button>
                      <span className="text-sm text-gray-600">
                        {currentImageIndex + 1} / {dicomSeries[currentSeriesIndex]?.files.length || 0}
                      </span>
                      <Button onClick={nextImage} variant="outline" size="sm">
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 图像信息 */}
            <Card>
              <CardHeader>
                <CardTitle>图像信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">缩放:</span>
                    <span>{viewportInfo.zoom.toFixed(2)}x</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">窗宽:</span>
                    <span>{viewportInfo.windowWidth}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">窗位:</span>
                    <span>{viewportInfo.windowCenter}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧图像显示区域 */}
          <div className="lg:col-span-3">
            <Card className="h-[600px]">
              <CardContent className="p-0 h-full">
                {isLoading && (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="mt-4 text-gray-600">加载中...</p>
                    </div>
                  </div>
                )}
                
                {error && (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <p className="text-red-600 mb-2">错误</p>
                      <p className="text-gray-600">{error}</p>
                    </div>
                  </div>
                )}
                
                {!isLoading && !error && (
                  <div className="h-full flex items-center justify-center bg-black">
                    <div 
                      ref={viewportRef}
                      className="w-full h-full"
                      style={{ minHeight: '400px' }}
                    />
                    {!renderingEngine && (
                      <div className="absolute inset-0 flex items-center justify-center text-white">
                        <p>请上传DICOM文件开始查看</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleDicomViewer;