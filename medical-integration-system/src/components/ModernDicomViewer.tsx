import React, { useEffect, useRef, useState, useCallback } from 'react';
import { 
  RenderingEngine, 
  Enums, 
  setVolumesForViewports,
  cache,
  imageLoader,
  metaData,
  init
} from '@cornerstonejs/core';
import { 
  addTool, 
  ToolGroupManager, 
  WindowLevelTool, 
  PanTool, 
  ZoomTool, 
  StackScrollTool,
  Enums as ToolEnums
} from '@cornerstonejs/tools';
import cornerstoneDICOMImageLoader from '@cornerstonejs/dicom-image-loader';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Upload, ZoomIn, ZoomOut, RotateCw, RefreshCw, ChevronLeft, ChevronRight, Tabs } from 'lucide-react';
import DicomFileLoader from './DicomFileLoader';

// 全局标志，确保Cornerstone3D只初始化一次
let isCornerstone3DInitialized = false;

// 初始化Cornerstone3D
const initializeCornerstone3D = async () => {
  if (isCornerstone3DInitialized) {
    console.log('Cornerstone3D already initialized, skipping...');
    return;
  }
  
  try {
    // Initialize Cornerstone3D core
    await init();
    
    // Initialize DICOM image loader - 完全禁用worker功能
    await cornerstoneDICOMImageLoader.init({
      maxWebWorkers: 0,
      startWebWorkersOnDemand: false,
      useWebWorkers: false
    });
    
    // Add tools
    addTool(WindowLevelTool);
    addTool(PanTool);
    addTool(ZoomTool);
    addTool(StackScrollTool);
    
    isCornerstone3DInitialized = true;
    console.log('Cornerstone3D initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Cornerstone3D:', error);
  }
};

interface DicomFile {
  file: File;
  imageId: string;
  metadata?: any;
}

interface DicomSeries {
  seriesInstanceUID: string;
  files: DicomFile[];
  description?: string;
}

const ModernDicomViewer: React.FC = () => {
  const viewportRef = useRef<HTMLDivElement>(null);
  const [renderingEngine, setRenderingEngine] = useState<RenderingEngine | null>(null);
  const [dicomSeries, setDicomSeries] = useState<DicomSeries[]>([]);
  const [currentSeriesIndex, setCurrentSeriesIndex] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewportInfo, setViewportInfo] = useState({
    zoom: 1,
    windowWidth: 0,
    windowCenter: 0
  });
  const [activeTab, setActiveTab] = useState<'upload' | 'browse'>('browse');

  const viewportId = 'CT_AXIAL_STACK';
  const renderingEngineId = 'myRenderingEngine';
  const toolGroupId = 'STACK_TOOL_GROUP_ID';

  // 初始化Cornerstone3D
  useEffect(() => {
    let engine: RenderingEngine | null = null;
    
    const init = async () => {
      try {
        await initializeCornerstone3D();
        
        if (viewportRef.current && !renderingEngine) {
          engine = new RenderingEngine(renderingEngineId);
          setRenderingEngine(engine);

          // 创建视口
          const viewportInput = {
            viewportId,
            type: Enums.ViewportType.STACK,
            element: viewportRef.current,
            defaultOptions: {
              background: [0.2, 0.3, 0.4] as [number, number, number],
            },
          };

          engine.enableElement(viewportInput);

          // 设置工具组
          let toolGroup = ToolGroupManager.getToolGroup(toolGroupId);
          if (!toolGroup) {
            toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
          }
          
          if (toolGroup) {
            // 检查工具是否已添加，避免重复添加
            if (!toolGroup.getToolInstance(WindowLevelTool.toolName)) {
              toolGroup.addTool(WindowLevelTool.toolName);
              toolGroup.addTool(PanTool.toolName);
              toolGroup.addTool(ZoomTool.toolName);
              toolGroup.addTool(StackScrollTool.toolName);

              toolGroup.setToolActive(WindowLevelTool.toolName, {
                bindings: [{ mouseButton: ToolEnums.MouseBindings.Primary }],
              });
              toolGroup.setToolActive(PanTool.toolName, {
                bindings: [{ mouseButton: ToolEnums.MouseBindings.Auxiliary }],
              });
              toolGroup.setToolActive(ZoomTool.toolName, {
                bindings: [{ mouseButton: ToolEnums.MouseBindings.Secondary }],
              });
              toolGroup.setToolActive(StackScrollTool.toolName, {
                 bindings: [{ mouseButton: ToolEnums.MouseBindings.Wheel }],
               });
            }

            toolGroup.addViewport(viewportId, renderingEngineId);
          }
        }
      } catch (err) {
        console.error('Failed to initialize Cornerstone3D:', err);
        setError('初始化DICOM查看器失败');
      }
    };

    init();

    return () => {
      // 清理资源
      if (engine) {
        engine.destroy();
      }
    };
  }, []); // 移除依赖数组中的renderingEngine，只在组件挂载时运行一次

  // 显示图像
  const displayImage = useCallback(async (imageId: string) => {
    if (!renderingEngine) return;

    try {
      const viewport = renderingEngine.getViewport(viewportId);
      if (viewport) {
        await viewport.setStack([imageId], 0);
        viewport.render();

        // 更新视口信息
        const image = cache.getImage(imageId);
        if (image) {
          setViewportInfo({
            zoom: viewport.getZoom(),
            windowWidth: image.windowWidth || 0,
            windowCenter: image.windowCenter || 0
          });
        }
      }
    } catch (err) {
      console.error('Error displaying image:', err);
      setError('显示图像时出错');
    }
  }, [renderingEngine]);

  const handleRealDicomFileLoad = useCallback(async (filePath: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // 将完整文件系统路径转换为相对于public目录的路径
      let staticUrl = filePath;
      if (filePath.includes('/demo-web/')) {
        // 提取demo-web之后的路径部分
        staticUrl = filePath.split('/demo-web/')[1];
      }
      // 确保路径以/开头
      if (!staticUrl.startsWith('/')) {
        staticUrl = `/${staticUrl}`;
      }
      console.log('Loading DICOM file from:', staticUrl);
      
      const response = await fetch(staticUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const blob = await response.blob();
      const file = new File([blob], filePath.split('/').pop() || 'dicom.dcm', { type: 'application/dicom' });
      
      // 创建图像ID
      const imageId = cornerstoneDICOMImageLoader.wadouri.fileManager.add(file);
      
      try {
        // 加载图像
        const image = await imageLoader.loadAndCacheImage(imageId);
        const metadata = metaData.get('generalSeriesModule', imageId);
        
        const dicomFile: DicomFile = {
          file,
          imageId,
          metadata: {
            ...metadata,
            instanceNumber: 1,
            seriesInstanceUID: metadata?.seriesInstanceUID || 'real-dicom-series'
          }
        };

        const series: DicomSeries = {
          seriesInstanceUID: dicomFile.metadata.seriesInstanceUID,
          files: [dicomFile],
          description: `真实DICOM文件: ${file.name}`
        };

        setDicomSeries([series]);
        setCurrentSeriesIndex(0);
        setCurrentImageIndex(0);

        // 显示图像
        await displayImage(imageId);
        
      } catch (err) {
        console.error(`Failed to load DICOM file ${filePath}:`, err);
        setError(`加载DICOM文件失败: ${err instanceof Error ? err.message : '未知错误'}`);
      }
    } catch (err) {
      console.error('Error fetching DICOM file:', err);
      setError('获取DICOM文件失败，请确保文件路径正确');
    } finally {
      setIsLoading(false);
    }
  }, [displayImage]);

  // 处理文件上传
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      const dicomFiles: DicomFile[] = [];
      const seriesMap = new Map<string, DicomFile[]>();

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // 创建图像ID
        const imageId = cornerstoneDICOMImageLoader.wadouri.fileManager.add(file);
        
        try {
          // 加载图像以获取元数据
          const image = await imageLoader.loadAndCacheImage(imageId);
          const metadata = metaData.get('generalSeriesModule', imageId);
          
          const dicomFile: DicomFile = {
            file,
            imageId,
            metadata: {
              ...metadata,
              instanceNumber: metaData.get('generalImageModule', imageId)?.instanceNumber || i + 1,
              seriesInstanceUID: metadata?.seriesInstanceUID || 'default-series'
            }
          };

          dicomFiles.push(dicomFile);

          // 按系列分组
          const seriesUID = dicomFile.metadata.seriesInstanceUID;
          if (!seriesMap.has(seriesUID)) {
            seriesMap.set(seriesUID, []);
          }
          seriesMap.get(seriesUID)!.push(dicomFile);
        } catch (err) {
          console.warn(`Failed to load image ${file.name}:`, err);
        }
      }

      // 创建系列数组并排序
      const series: DicomSeries[] = Array.from(seriesMap.entries()).map(([seriesUID, files]) => {
        // 按实例号排序
        const sortedFiles = files.sort((a, b) => {
          const aInstance = a.metadata?.instanceNumber || 0;
          const bInstance = b.metadata?.instanceNumber || 0;
          return aInstance - bInstance;
        });

        return {
          seriesInstanceUID: seriesUID,
          files: sortedFiles,
          description: files[0]?.metadata?.seriesDescription || `Series ${seriesUID.slice(-8)}`
        };
      });

      setDicomSeries(series);
      setCurrentSeriesIndex(0);
      setCurrentImageIndex(0);

      // 显示第一张图像
      if (series.length > 0 && series[0].files.length > 0) {
        await displayImage(series[0].files[0].imageId);
      }

    } catch (err) {
      console.error('Error processing DICOM files:', err);
      setError('处理DICOM文件时出错');
    } finally {
      setIsLoading(false);
    }
  }, [renderingEngine]);

  // 导航函数
  const navigateImage = useCallback((direction: 'prev' | 'next') => {
    if (dicomSeries.length === 0) return;

    const currentSeries = dicomSeries[currentSeriesIndex];
    if (!currentSeries || currentSeries.files.length === 0) return;

    let newImageIndex = currentImageIndex;
    
    if (direction === 'next') {
      newImageIndex = (currentImageIndex + 1) % currentSeries.files.length;
    } else {
      newImageIndex = currentImageIndex === 0 ? currentSeries.files.length - 1 : currentImageIndex - 1;
    }

    setCurrentImageIndex(newImageIndex);
    displayImage(currentSeries.files[newImageIndex].imageId);
  }, [dicomSeries, currentSeriesIndex, currentImageIndex, displayImage]);

  // 缩放函数
  const handleZoom = useCallback((factor: number) => {
    if (!renderingEngine) return;

    const viewport = renderingEngine.getViewport(viewportId);
    if (viewport) {
      const currentZoom = viewport.getZoom();
      viewport.setZoom(currentZoom * factor);
      viewport.render();
      setViewportInfo(prev => ({ ...prev, zoom: currentZoom * factor }));
    }
  }, [renderingEngine]);

  // 重置视图
  const handleReset = useCallback(() => {
    if (!renderingEngine) return;

    const viewport = renderingEngine.getViewport(viewportId);
    if (viewport) {
      viewport.resetCamera();
      viewport.render();
      setViewportInfo(prev => ({ ...prev, zoom: 1 }));
    }
  }, [renderingEngine]);

  const currentSeries = dicomSeries[currentSeriesIndex];
  const currentFile = currentSeries?.files[currentImageIndex];

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      {/* 头部工具栏 */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-800">现代化DICOM查看器</h1>
          <div className="text-sm text-gray-500">
            基于Cornerstone3D的专业医学影像查看器
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏 */}
        <div className="w-80 bg-white border-r overflow-y-auto">
          <div className="p-4">
            {/* 标签页切换 */}
            <div className="flex mb-4 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('browse')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'browse'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                浏览文件
              </button>
              <button
                onClick={() => setActiveTab('upload')}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'upload'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                上传文件
              </button>
            </div>

            {/* 文件浏览器 */}
            {activeTab === 'browse' && (
              <DicomFileLoader onFileSelect={handleRealDicomFileLoad} />
            )}

            {/* 文件上传 */}
            {activeTab === 'upload' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">上传DICOM文件</CardTitle>
                </CardHeader>
                <CardContent>
                  <input
                    type="file"
                    multiple
                    accept=".dcm,.dicom"
                    onChange={handleFileUpload}
                    className="hidden"
                    id="dicom-upload"
                  />
                  <label htmlFor="dicom-upload">
                    <Button variant="outline" className="w-full cursor-pointer">
                      <Upload className="w-4 h-4 mr-2" />
                      选择DICOM文件
                    </Button>
                  </label>
                  <p className="text-xs text-gray-500 mt-2">
                    支持 .dcm 和 .dicom 格式，可多选
                  </p>
                </CardContent>
              </Card>
            )}

            {/* 系列信息 */}
            {dicomSeries.length > 0 && (
              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-sm">系列信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {dicomSeries.map((series, index) => (
                      <div
                        key={series.seriesInstanceUID}
                        className={`p-2 rounded cursor-pointer ${
                          index === currentSeriesIndex ? 'bg-blue-100' : 'bg-gray-50'
                        }`}
                        onClick={() => {
                          setCurrentSeriesIndex(index);
                          setCurrentImageIndex(0);
                          if (series.files.length > 0) {
                            displayImage(series.files[0].imageId);
                          }
                        }}
                      >
                        <div className="text-sm font-medium">{series.description}</div>
                        <div className="text-xs text-gray-500">
                          {series.files.length} 张图像
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {currentFile && (
              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-sm">图像信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-xs">
                    <div>文件: {currentFile.file.name}</div>
                    <div>图像: {currentImageIndex + 1} / {currentSeries?.files.length || 0}</div>
                    <div>缩放: {viewportInfo.zoom.toFixed(2)}x</div>
                    {viewportInfo.windowWidth > 0 && (
                      <>
                        <div>窗宽: {viewportInfo.windowWidth}</div>
                        <div>窗位: {viewportInfo.windowCenter}</div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* 主视图区域 */}
        <div className="flex-1 flex flex-col">
          {/* 控制工具栏 */}
          <div className="bg-white border-b p-2">
            <div className="flex items-center justify-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateImage('prev')}
                disabled={!currentSeries || currentSeries.files.length <= 1}
              >
                <ChevronLeft className="w-4 h-4" />
                上一张
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateImage('next')}
                disabled={!currentSeries || currentSeries.files.length <= 1}
              >
                下一张
                <ChevronRight className="w-4 h-4" />
              </Button>

              <div className="w-px h-6 bg-gray-300 mx-2" />

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleZoom(1.2)}
              >
                <ZoomIn className="w-4 h-4" />
                放大
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleZoom(0.8)}
              >
                <ZoomOut className="w-4 h-4" />
                缩小
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
              >
                <RefreshCw className="w-4 h-4" />
                重置
              </Button>
            </div>
          </div>

          {/* DICOM视口 */}
          <div className="flex-1 bg-black relative">
            <div
              ref={viewportRef}
              className="w-full h-full"
              style={{ minHeight: '400px' }}
            />
            
            {isLoading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <div className="text-white">加载中...</div>
              </div>
            )}

            {error && (
              <div className="absolute top-4 left-4 bg-red-500 text-white p-2 rounded">
                {error}
              </div>
            )}

            {dicomSeries.length === 0 && !isLoading && (
              <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <Upload className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p>请上传DICOM文件开始查看</p>
                  <p className="text-sm mt-2">支持 .dcm 和 .dicom 格式</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernDicomViewer;