import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Stethoscope, Monitor, Users, FileText } from 'lucide-react';
import { Toaster } from 'sonner';
import ClinicalWorkspace from './components/ClinicalWorkspace';
import RadiologyWorkspace from './components/RadiologyWorkspace';
import DwvDicomViewer from './components/DwvDicomViewer';

type WorkspaceType = 'clinical' | 'radiology';

function MainLayout() {
  const [activeWorkspace, setActiveWorkspace] = useState<WorkspaceType>('clinical');

  const workspaces = [
    {
      id: 'clinical' as WorkspaceType,
      name: '临床医生',
      icon: Stethoscope,
      description: 'HIS门诊病历录入系统',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      hoverColor: 'hover:bg-green-100'
    },
    {
      id: 'radiology' as WorkspaceType,
      name: '影像科医生',
      icon: Monitor,
      description: '医学影像报告系统',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      hoverColor: 'hover:bg-blue-100'
    }
  ];

  const currentWorkspace = workspaces.find(w => w.id === activeWorkspace);

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部导航栏 */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* 系统标题 */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">医疗信息系统</h1>
                <p className="text-sm text-gray-500">Medical Information Integration System</p>
              </div>
            </div>

            {/* 当前工作区信息 */}
            <div className="flex items-center space-x-2">
              {currentWorkspace && (
                <>
                  <currentWorkspace.icon className={`w-5 h-5 ${currentWorkspace.color}`} />
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-800">{currentWorkspace.name}</div>
                    <div className="text-xs text-gray-500">{currentWorkspace.description}</div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 工作区切换标签 */}
        <div className="px-6">
          <div className="flex space-x-1">
            {workspaces.map((workspace) => {
              const Icon = workspace.icon;
              const isActive = activeWorkspace === workspace.id;
              
              return (
                <button
                  key={workspace.id}
                  onClick={() => setActiveWorkspace(workspace.id)}
                  className={`flex items-center space-x-2 px-4 py-3 rounded-t-lg font-medium text-sm transition-all duration-200 ${
                    isActive
                      ? `${workspace.bgColor} ${workspace.color} border-b-2 border-current`
                      : `text-gray-600 hover:text-gray-800 ${workspace.hoverColor}`
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{workspace.name}</span>
                  {isActive && (
                    <div className="w-2 h-2 bg-current rounded-full opacity-60"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-1 overflow-hidden">
        {activeWorkspace === 'clinical' && <ClinicalWorkspace />}
        {activeWorkspace === 'radiology' && <RadiologyWorkspace />}
      </main>

      {/* Toast 通知 */}
      <Toaster 
        position="top-right" 
        richColors 
        closeButton
        toastOptions={{
          duration: 3000,
          style: {
            fontSize: '14px'
          }
        }}
      />
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<MainLayout />} />
        <Route path="/dicom-viewer" element={<DwvDicomViewer />} />
      </Routes>
    </Router>
  );
}

export default App;
