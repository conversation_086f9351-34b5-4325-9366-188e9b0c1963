/**
 * Test utility for DICOM upload functionality
 * This file provides functions to test DICOM file processing without actual file uploads
 */

import { processUploadedFiles } from './dicomUtils';

/**
 * Creates a mock File object for testing
 */
function createMockFile(name: string, content: ArrayBuffer, type: string = 'application/dicom'): File {
  const blob = new Blob([content], { type });
  const file = new File([blob], name, { type });
  return file;
}

/**
 * Creates a simple mock DICOM file buffer for testing
 * This creates a minimal DICOM file structure
 */
function createMockDicomBuffer(): ArrayBuffer {
  // Create a minimal DICOM file structure
  // This is a very basic implementation for testing purposes
  const buffer = new ArrayBuffer(1024);
  const view = new Uint8Array(buffer);
  
  // DICOM file preamble (128 bytes of zeros)
  for (let i = 0; i < 128; i++) {
    view[i] = 0;
  }
  
  // DICOM prefix "DICM"
  view[128] = 0x44; // 'D'
  view[129] = 0x49; // 'I'
  view[130] = 0x43; // 'C'
  view[131] = 0x4D; // 'M'
  
  // Add some basic DICOM tags (this is simplified)
  // In a real DICOM file, this would be much more complex
  let offset = 132;
  
  // Add a simple tag structure (Group, Element, VR, Length, Value)
  // This is a very simplified version and may not work with real parsers
  
  return buffer;
}

/**
 * Test function to validate DICOM upload processing
 */
export async function testDicomUpload(): Promise<void> {
  console.log('Testing DICOM upload functionality...');
  
  try {
    // Create mock DICOM files
    const mockBuffer1 = createMockDicomBuffer();
    const mockBuffer2 = createMockDicomBuffer();
    
    const mockFile1 = createMockFile('test1.dcm', mockBuffer1);
    const mockFile2 = createMockFile('test2.dcm', mockBuffer2);
    
    const files = [mockFile1, mockFile2];
    
    console.log(`Created ${files.length} mock DICOM files for testing`);
    
    // Test the upload processing
    const studies = await processUploadedFiles(files);
    
    console.log('Upload processing completed');
    console.log(`Processed ${studies.length} studies`);
    
    studies.forEach((study, index) => {
      console.log(`Study ${index + 1}:`, {
        studyInstanceUID: study.studyInstanceUID,
        patientId: study.patientId,
        studyDate: study.studyDate,
        seriesCount: study.series.length
      });
      
      study.series.forEach((series, seriesIndex) => {
        console.log(`  Series ${seriesIndex + 1}:`, {
          seriesInstanceUID: series.seriesInstanceUID,
          modality: series.modality,
          fileCount: series.files.length
        });
      });
    });
    
  } catch (error) {
    console.error('DICOM upload test failed:', error);
  }
}

/**
 * Test function to validate file selection behavior
 */
export function testFileSelection(): void {
  console.log('Testing file selection behavior...');
  
  // Create a temporary file input to test behavior
  const input = document.createElement('input');
  input.type = 'file';
  input.multiple = true;
  input.webkitdirectory = true;
  input.accept = '.dcm,.dicom';
  
  input.addEventListener('change', (event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    
    if (files && files.length > 0) {
      console.log(`Selected ${files.length} files`);
      console.log('File details:', Array.from(files).map(f => ({
        name: f.name,
        size: f.size,
        type: f.type,
        relativePath: f.webkitRelativePath
      })));
    } else {
      console.log('No files selected');
    }
  });
  
  // Trigger file selection dialog
  input.click();
}

/**
 * Validates DICOM file extension
 */
export function isDicomFile(fileName: string): boolean {
  const dicomExtensions = ['.dcm', '.dicom', '.dic'];
  const lowerFileName = fileName.toLowerCase();
  return dicomExtensions.some(ext => lowerFileName.endsWith(ext));
}

/**
 * Filters files to only include DICOM files
 */
export function filterDicomFiles(files: FileList | File[]): File[] {
  const fileArray = Array.from(files);
  return fileArray.filter(file => isDicomFile(file.name));
}