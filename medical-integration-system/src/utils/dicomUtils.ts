import dicomParser from 'dicom-parser';
import { DataSet } from 'dicom-parser';

// ----------------- INTERFACES -----------------

export interface DicomMetadata {
  StudyInstanceUID?: string;
  SeriesInstanceUID?: string;
  SOPInstanceUID?: string;
  PatientID?: string;
  PatientName?: string;
  StudyDate?: string;
  StudyDescription?: string;
  SeriesDescription?: string;
  Modality?: string;
  InstanceNumber?: number;
  ImagePositionPatient?: number[];
  ImageOrientationPatient?: number[];
  PixelSpacing?: number[];
  SliceThickness?: number;
  WindowCenter?: number;
  WindowWidth?: number;
  RescaleSlope?: number;
  RescaleIntercept?: number;
  PhotometricInterpretation?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // Allow other tags
}

export interface DicomFile {
  id: string;
  fileName: string;
  metadata: DicomMetadata;
  pixelData: Int16Array | Uint16Array | Uint8Array | Int8Array;
  width: number;
  height: number;
  getDisplayData: (options: { windowWidth?: number; windowCenter?: number }) => Uint8ClampedArray;
}

export interface DicomSeries {
  seriesInstanceUID: string;
  seriesDescription: string;
  modality: string;
  files: DicomFile[];
}

export interface DicomStudy {
  studyInstanceUID: string;
  patientId: string;
  studyDate: string;
  studyDescription: string;
  series: DicomSeries[];
}

// ----------------- MOCK DATA GENERATION -----------------

// (Existing mock data functions: createMockDicomData, createMockDicomImageData, etc. are assumed to be here)
// To keep the code concise, I will omit the full implementation of mock data functions as they are not the focus of the current change.
// A placeholder is used to represent them.

export async function createMockDicomData(): Promise<DicomStudy[]> {
  console.log("Generating mock DICOM data.");
  
  // 创建mock DICOM文件
  const createMockDicomFile = (id: string, fileName: string, instanceNumber: number): DicomFile => ({
    id,
    fileName,
    metadata: {
      StudyInstanceUID: '1.2.840.113619.2.55.3.514611216.984.**********.520',
      SeriesInstanceUID: '1.2.840.113619.2.55.3.514611216.984.**********.521',
      SOPInstanceUID: `1.2.840.113619.2.55.3.514611216.984.**********.520.${instanceNumber}`,
      PatientID: 'P001',
      PatientName: '张三',
      StudyDate: '20240101',
      StudyDescription: '胸部CT检查',
      SeriesDescription: '肋骨序列',
      Modality: 'CT',
      InstanceNumber: instanceNumber,
      WindowCenter: 400,
      WindowWidth: 1000,
      RescaleSlope: 1,
      RescaleIntercept: -1024,
      PhotometricInterpretation: 'MONOCHROME2',
    },
    pixelData: new Uint16Array(512 * 512), // 创建空的像素数据
    width: 512,
    height: 512,
    getDisplayData: (options) => {
      const pixelData = new Uint16Array(512 * 512);
      // 创建一个简单的测试图案
      for (let i = 0; i < pixelData.length; i++) {
        const x = i % 512;
        const y = Math.floor(i / 512);
        // 创建一个渐变图案
        pixelData[i] = Math.floor((x + y) / 2);
      }
      
      return convertToDisplayData(
        pixelData,
        512,
        512,
        {
          windowWidth: options?.windowWidth || 1000,
          windowCenter: options?.windowCenter || 400,
          rescaleSlope: 1,
          rescaleIntercept: -1024,
          photometricInterpretation: 'MONOCHROME2',
        }
      );
    }
  });

  // 创建mock序列
  const mockSeries: DicomSeries = {
    seriesInstanceUID: '1.2.840.113619.2.55.3.514611216.984.**********.521',
    seriesDescription: '肋骨序列',
    modality: 'CT',
    files: [
      createMockDicomFile('1', '1.2.840.113619.2.55.3.514611216.984.**********.520.1.dcm', 1),
      createMockDicomFile('2', '1.2.840.113619.2.55.3.514611216.984.**********.520.2.dcm', 2),
      createMockDicomFile('3', '1.2.840.113619.2.55.3.514611216.984.**********.520.3.dcm', 3),
    ]
  };

  // 创建mock研究
  const mockStudy: DicomStudy = {
    studyInstanceUID: '1.2.840.113619.2.55.3.514611216.984.**********.520',
    patientId: 'P001',
    studyDate: '20240101',
    studyDescription: '胸部CT检查',
    series: [mockSeries]
  };

  return [mockStudy];
}


// ----------------- CORE DICOM PROCESSING LOGIC -----------------

/**
 * Scans a directory for DICOM files. 
 * NOTE: This function is currently mocked to support web-based file uploads.
 * It will return mock data by default.
 */
export async function scanDicomDirectory(basePath: string): Promise<DicomStudy[]> {
  console.log(`Scanning DICOM directory: ${basePath}`);
  try {
    console.warn('scanDicomDirectory now returns mock data. Use file upload to load real DICOM files.');
    return await createMockDicomData();
  } catch (error) {
    console.error('Error scanning DICOM directory, falling back to mock data:', error);
    return await createMockDicomData();
  }
}

/**
 * Parses a single DICOM file from a File object.
 */
export function parseDicomFromFile(file: File): Promise<DicomFile> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (event) => {
      if (event.target?.result) {
        try {
          const arrayBuffer = event.target.result as ArrayBuffer;
          const dicomFile = await parseDicomFromBuffer(arrayBuffer, file.name);
          resolve(dicomFile);
        } catch (error) {
          reject(error);
        }
      } else {
        reject(new Error(`Failed to read file: ${file.name}`));
      }
    };
    reader.onerror = () => reject(new Error(`Error reading file: ${file.name}`));
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Processes an array of uploaded DICOM files and organizes them into studies and series.
 */
export async function processUploadedFiles(files: File[]): Promise<DicomStudy[]> {
  const dicomFilePromises = files.map(file => parseDicomFromFile(file).catch(() => null));
  const dicomFiles = (await Promise.all(dicomFilePromises)).filter((f): f is DicomFile => f !== null);

  const studies: { [key: string]: DicomStudy } = {};

  dicomFiles.forEach(dicomFile => {
    const studyUID = dicomFile.metadata.StudyInstanceUID || 'UnknownStudy';
    const seriesUID = dicomFile.metadata.SeriesInstanceUID || 'UnknownSeries';

    if (!studies[studyUID]) {
      studies[studyUID] = {
        studyInstanceUID: studyUID,
        patientId: dicomFile.metadata.PatientID || 'UnknownPatient',
        studyDate: dicomFile.metadata.StudyDate || 'UnknownDate',
        studyDescription: dicomFile.metadata.StudyDescription || 'UnknownStudy',
        series: [],
      };
    }

    let series = studies[studyUID].series.find(s => s.seriesInstanceUID === seriesUID);
    if (!series) {
      series = {
        seriesInstanceUID: seriesUID,
        seriesDescription: dicomFile.metadata.SeriesDescription || 'UnknownSeries',
        modality: dicomFile.metadata.Modality || 'Unknown',
        files: [],
      };
      studies[studyUID].series.push(series);
    }
    series.files.push(dicomFile);
  });

  // Sort files within each series by instance number
  Object.values(studies).forEach(study => {
    study.series.forEach(series => {
      series.files.sort((a, b) => (a.metadata.InstanceNumber || 0) - (b.metadata.InstanceNumber || 0));
    });
  });

  return Object.values(studies);
}


/**
 * Parses DICOM data from an ArrayBuffer.
 */
export async function parseDicomFromBuffer(arrayBuffer: ArrayBuffer, fileName: string): Promise<DicomFile> {
  try {
    const dataSet = dicomParser.parseDicom(new Uint8Array(arrayBuffer));

    const metadata: DicomMetadata = {
      StudyInstanceUID: getDicomTag(dataSet, 'x0020000d'),
      SeriesInstanceUID: getDicomTag(dataSet, 'x0020000e'),
      SOPInstanceUID: getDicomTag(dataSet, 'x00080018'),
      PatientID: getDicomTag(dataSet, 'x00100020'),
      PatientName: getDicomTag(dataSet, 'x00100010'),
      StudyDate: getDicomTag(dataSet, 'x00080020'),
      StudyDescription: getDicomTag(dataSet, 'x00081030'),
      SeriesDescription: getDicomTag(dataSet, 'x0008103e'),
      Modality: getDicomTag(dataSet, 'x00080060'),
      InstanceNumber: parseInt(getDicomTag(dataSet, 'x00200013') || '0', 10),
      WindowCenter: parseFloat(getDicomTag(dataSet, 'x00281050') || '0'),
      WindowWidth: parseFloat(getDicomTag(dataSet, 'x00281051') || '0'),
      RescaleSlope: parseFloat(getDicomTag(dataSet, 'x00281053') || '1'),
      RescaleIntercept: parseFloat(getDicomTag(dataSet, 'x00281052') || '0'),
      PhotometricInterpretation: getDicomTag(dataSet, 'x00280004'),
    };

    const { pixelData, width, height } = extractImageData(dataSet);

    const dicomFile: DicomFile = {
      id: metadata.SOPInstanceUID || `${fileName}-${Date.now()}`,
      fileName,
      metadata,
      pixelData,
      width,
      height,
      getDisplayData: (options) => {
        return convertToDisplayData(
          pixelData,
          width,
          height,
          {
            windowWidth: options.windowWidth ?? metadata.WindowWidth,
            windowCenter: options.windowCenter ?? metadata.WindowCenter,
            rescaleSlope: metadata.RescaleSlope,
            rescaleIntercept: metadata.RescaleIntercept,
            photometricInterpretation: metadata.PhotometricInterpretation,
          }
        );
      },
    };

    return dicomFile;
  } catch (error) {
    console.error(`Failed to parse DICOM file ${fileName}:`, error);
    throw new Error(`Failed to parse DICOM file ${fileName}`);
  }
}

/**
 * Extracts image data from a DICOM dataSet.
 */
function extractImageData(dataSet: DataSet) {
  const pixelDataElement = dataSet.elements.x7fe00010;
  if (!pixelDataElement) {
    throw new Error('PixelData not found in DICOM file.');
  }

  const width = dataSet.uint16('x00280011');
  const height = dataSet.uint16('x00280010');
  const bitsAllocated = dataSet.uint16('x00280100');

  if (!width || !height || !bitsAllocated) {
    throw new Error('Essential image metadata (width, height, bitsAllocated) not found.');
  }

  const pixelDataOffset = pixelDataElement.dataOffset;
  const pixelDataLength = pixelDataElement.length;

  let pixelData;
  if (bitsAllocated === 16) {
    pixelData = new Int16Array(dataSet.byteArray.buffer, pixelDataOffset, pixelDataLength / 2);
  } else if (bitsAllocated === 8) {
    pixelData = new Uint8Array(dataSet.byteArray.buffer, pixelDataOffset, pixelDataLength);
  } else {
    throw new Error(`Unsupported bitsAllocated: ${bitsAllocated}`);
  }

  return { pixelData, width, height };
}

/**
 * Helper to get a string value from a DICOM tag.
 */
export function getDicomTag(dataSet: DataSet, tag: string): string | undefined {
  const element = dataSet.elements[tag];
  return element ? dataSet.string(tag) : undefined;
}

/**
 * Converts raw pixel data to a displayable image data array.
 */
export function convertToDisplayData(
  pixelData: Int16Array | Uint16Array | Uint8Array | Int8Array,
  width: number,
  height: number,
  options: {
    windowWidth?: number;
    windowCenter?: number;
    rescaleSlope?: number;
    rescaleIntercept?: number;
    photometricInterpretation?: string;
  }
): Uint8ClampedArray {
  const {
    windowWidth = 400,
    windowCenter = 40,
    rescaleSlope = 1,
    rescaleIntercept = 0,
    photometricInterpretation,
  } = options;

  const displayData = new Uint8ClampedArray(width * height * 4);
  const lower = windowCenter - windowWidth / 2.0;
  const upper = windowCenter + windowWidth / 2.0;

  for (let i = 0; i < pixelData.length; i++) {
    const rawValue = pixelData[i] * rescaleSlope + rescaleIntercept;

    let intensity = 255.0 * ((rawValue - lower) / (upper - lower));
    if (photometricInterpretation === 'MONOCHROME1') {
      intensity = 255.0 - intensity;
    }

    intensity = Math.max(0, Math.min(255, intensity));

    const j = i * 4;
    displayData[j] = intensity;
    displayData[j + 1] = intensity;
    displayData[j + 2] = intensity;
    displayData[j + 3] = 255; // Alpha
  }

  return displayData;
}