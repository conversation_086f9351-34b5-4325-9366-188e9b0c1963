// 测试DICOM文件生成器
// 用于生成简单的测试DICOM文件

export const generateTestDicomFile = (): File => {
  // 创建一个简单的DICOM文件结构
  const buffer = new ArrayBuffer(1024);
  const view = new DataView(buffer);
  
  // DICOM文件前缀 (128字节)
  for (let i = 0; i < 128; i++) {
    view.setUint8(i, 0);
  }
  
  // DICM标识符
  view.setUint8(128, 0x44); // 'D'
  view.setUint8(129, 0x49); // 'I'
  view.setUint8(130, 0x43); // 'C'
  view.setUint8(131, 0x4D); // 'M'
  
  // 简单的元数据标签
  let offset = 132;
  
  // Transfer Syntax UID (0002,0010)
  view.setUint16(offset, 0x0002, true); offset += 2;
  view.setUint16(offset, 0x0010, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: UI
  view.setUint8(offset, 0x49); offset += 1;
  view.setUint16(offset, 26, true); offset += 2; // Length
  // Transfer Syntax UID value
  const transferSyntax = '1.2.840.10008.1.2.1\0';
  for (let i = 0; i < transferSyntax.length; i++) {
    view.setUint8(offset + i, transferSyntax.charCodeAt(i));
  }
  offset += 26;
  
  // Rows (0028,0010)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0010, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 256, true); offset += 2; // 256 rows
  
  // Columns (0028,0011)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0011, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 256, true); offset += 2; // 256 columns
  
  // Bits Allocated (0028,0100)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0100, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 16, true); offset += 2; // 16 bits
  
  // Pixel Data (7FE0,0010) - 简化版本
  view.setUint16(offset, 0x7FE0, true); offset += 2;
  view.setUint16(offset, 0x0010, true); offset += 2;
  view.setUint8(offset, 0x4F); offset += 1; // VR: OW
  view.setUint8(offset, 0x57); offset += 1;
  view.setUint16(offset, 0, true); offset += 2; // Reserved
  view.setUint32(offset, 256 * 256 * 2, true); offset += 4; // Length
  
  // 生成简单的测试图像数据 (256x256, 16-bit)
  for (let i = 0; i < 256 * 256 && offset + 1 < buffer.byteLength; i++) {
    const value = Math.floor(Math.random() * 65535);
    view.setUint16(offset, value, true);
    offset += 2;
  }
  
  // 创建File对象
  const blob = new Blob([buffer], { type: 'application/dicom' });
  return new File([blob], 'test.dcm', { type: 'application/dicom' });
};

// 生成带有特定模式的测试图像
export const generateTestDicomWithPattern = (pattern: 'gradient' | 'checkerboard' | 'circle'): File => {
  const width = 256;
  const height = 256;
  const buffer = new ArrayBuffer(1024 + width * height * 2);
  const view = new DataView(buffer);
  
  // DICOM文件前缀 (128字节)
  for (let i = 0; i < 128; i++) {
    view.setUint8(i, 0);
  }
  
  // DICM标识符
  view.setUint8(128, 0x44); // 'D'
  view.setUint8(129, 0x49); // 'I'
  view.setUint8(130, 0x43); // 'C'
  view.setUint8(131, 0x4D); // 'M'
  
  // 简单的元数据标签
  let offset = 132;
  
  // Transfer Syntax UID (0002,0010)
  view.setUint16(offset, 0x0002, true); offset += 2;
  view.setUint16(offset, 0x0010, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: UI
  view.setUint8(offset, 0x49); offset += 1;
  view.setUint16(offset, 26, true); offset += 2; // Length
  // Transfer Syntax UID value
  const transferSyntax = '1.2.840.10008.1.2.1\0';
  for (let i = 0; i < transferSyntax.length; i++) {
    view.setUint8(offset + i, transferSyntax.charCodeAt(i));
  }
  offset += 26;
  
  // Rows (0028,0010)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0010, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, height, true); offset += 2; // height rows
  
  // Columns (0028,0011)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0011, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, width, true); offset += 2; // width columns
  
  // Bits Allocated (0028,0100)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0100, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 16, true); offset += 2; // 16 bits
  
  // Bits Stored (0028,0101)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0101, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 16, true); offset += 2; // 16 bits
  
  // High Bit (0028,0102)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0102, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 15, true); offset += 2; // 15 (0-based)
  
  // Pixel Representation (0028,0103)
  view.setUint16(offset, 0x0028, true); offset += 2;
  view.setUint16(offset, 0x0103, true); offset += 2;
  view.setUint8(offset, 0x55); offset += 1; // VR: US
  view.setUint8(offset, 0x53); offset += 1;
  view.setUint16(offset, 2, true); offset += 2; // Length
  view.setUint16(offset, 0, true); offset += 2; // 0 = unsigned
  
  // Pixel Data (7FE0,0010)
  view.setUint16(offset, 0x7FE0, true); offset += 2;
  view.setUint16(offset, 0x0010, true); offset += 2;
  view.setUint8(offset, 0x4F); offset += 1; // VR: OW
  view.setUint8(offset, 0x57); offset += 1;
  view.setUint16(offset, 0, true); offset += 2; // Reserved
  view.setUint32(offset, width * height * 2, true); offset += 4; // Length
  
  console.log(`生成${pattern}模式测试图像，尺寸: ${width}x${height}, 头部结束位置: ${offset}`);
  
  // 生成图像数据
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let value = 0;
      
      switch (pattern) {
        case 'gradient': {
          value = Math.floor((x / width) * 65535);
          break;
        }
        case 'checkerboard': {
          value = ((Math.floor(x / 32) + Math.floor(y / 32)) % 2) * 65535;
          break;
        }
        case 'circle': {
          const centerX = width / 2;
          const centerY = height / 2;
          const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
          value = distance < 64 ? 65535 : 0;
          break;
        }
      }
      
      if (offset + 1 < buffer.byteLength) {
        view.setUint16(offset, value, true);
        offset += 2;
      }
    }
  }
  
  const blob = new Blob([buffer], { type: 'application/dicom' });
  return new File([blob], `test_${pattern}.dcm`, { type: 'application/dicom' });
};