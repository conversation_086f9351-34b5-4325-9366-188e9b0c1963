import * as cornerstone from 'cornerstone-core';
import * as cornerstoneWADOImageLoader from 'cornerstone-wado-image-loader';
import * as dicomParser from 'dicom-parser';

// Define interfaces for cornerstone image and metadata
interface CornerstoneImage {
  imageId: string;
  minPixelValue: number;
  maxPixelValue: number;
  slope: number;
  intercept: number;
  windowCenter: number;
  windowWidth: number;
  rows: number;
  columns: number;
  height: number;
  width: number;
  color: boolean;
  columnPixelSpacing: number;
  rowPixelSpacing: number;
  invert: boolean;
  sizeInBytes: number;
  getPixelData: (frameIndex?: number) => number[];
  numberOfFrames: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  frames: any[];
  currentFrame: number;
  frameIncrementPointer: string;
  dataSet: dicomParser.DataSet;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  modalityLUT?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  voiLUT?: any;
}

interface DicomData {
  image: CornerstoneImage;
  dataSet: dicomParser.DataSet;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata: any;
}

// Initialize cornerstone and WADO image loader
export const initializeCornerstone = async (): Promise<void> => {
  try {
    // Initialize cornerstone
    cornerstone.enable(document.createElement('canvas'));
    
    // Configure WADO image loader with dicomParser
    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
    
    console.log('Cornerstone initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Cornerstone:', error);
    throw error;
  }
};

// Load DICOM image from file using dicomParser
export const loadDicomImage = async (file: File): Promise<DicomData> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer;
        const byteArray = new Uint8Array(arrayBuffer);
        
        // Parse DICOM file using dicomParser
        const dataSet = dicomParser.parseDicom(byteArray);
        
        // Extract image properties from DICOM dataset
        const rows = dataSet.uint16('x00280010') || 512; // Rows
        const columns = dataSet.uint16('x00280011') || 512; // Columns
        const bitsAllocated = dataSet.uint16('x00280100') || 16; // Bits Allocated
        const samplesPerPixel = dataSet.uint16('x00280002') || 1; // Samples per Pixel
        
        // Window Center and Width
        const windowCenter = dataSet.floatString('x00281050') || 2048; // Window Center
        const windowWidth = dataSet.floatString('x00281051') || 4096; // Window Width
        
        // Pixel Spacing
        const pixelSpacing = dataSet.string('x00280030'); // Pixel Spacing
        let rowPixelSpacing = 1;
        let columnPixelSpacing = 1;
        if (pixelSpacing) {
          const spacing = pixelSpacing.split('\\');
          if (spacing.length >= 2) {
            rowPixelSpacing = parseFloat(spacing[0]) || 1;
            columnPixelSpacing = parseFloat(spacing[1]) || 1;
          }
        }
        
        // Get pixel data element
        const pixelDataElement = dataSet.elements.x7fe00010;
        if (!pixelDataElement) {
          throw new Error('No pixel data found in DICOM file');
        }
        
        // Extract pixel data based on bits allocated
        let pixelData;
        if (bitsAllocated === 16) {
          pixelData = new Uint16Array(
            dataSet.byteArray.buffer,
            pixelDataElement.dataOffset,
            pixelDataElement.length / 2
          );
        } else {
          pixelData = new Uint8Array(
            dataSet.byteArray.buffer,
            pixelDataElement.dataOffset,
            pixelDataElement.length
          );
        }
        
        // Calculate min/max pixel values
        let minPixelValue = Number.MAX_VALUE;
        let maxPixelValue = Number.MIN_VALUE;
        for (let i = 0; i < pixelData.length; i++) {
          const value = pixelData[i];
          if (value < minPixelValue) minPixelValue = value;
          if (value > maxPixelValue) maxPixelValue = value;
        }
        
        // Check for multi-frame support <mcreference link="https://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.6.html" index="2">2</mcreference>
        const numberOfFrames = dataSet.intString('x00280008') || 1; // Number of Frames
        const frameIncrementPointer = dataSet.string('x00280009'); // Frame Increment Pointer
        
        // Handle multi-frame images
        const frames = [];
        if (numberOfFrames > 1) {
          const frameSize = (rows * columns * bitsAllocated) / 8;
          for (let frameIndex = 0; frameIndex < numberOfFrames; frameIndex++) {
            const frameOffset = frameIndex * frameSize;
            let framePixelData;
            
            if (bitsAllocated === 16) {
              framePixelData = new Uint16Array(
                dataSet.byteArray.buffer,
                pixelDataElement.dataOffset + frameOffset,
                frameSize / 2
              );
            } else {
              framePixelData = new Uint8Array(
                dataSet.byteArray.buffer,
                pixelDataElement.dataOffset + frameOffset,
                frameSize
              );
            }
            
            frames.push({
              frameIndex,
              pixelData: framePixelData,
              frameTime: frameIndex * 100 // Default frame time in ms
            });
          }
        } else {
          // Single frame
          frames.push({
            frameIndex: 0,
            pixelData: pixelData,
            frameTime: 0
          });
        }
        
        // Create cornerstone image object with multi-frame support
        const image: CornerstoneImage = {
          imageId: `dicom:${file.name}`,
          minPixelValue,
          maxPixelValue,
          slope: dataSet.floatString('x00281053') || 1, // Rescale Slope
          intercept: dataSet.floatString('x00281052') || 0, // Rescale Intercept
          windowCenter: Array.isArray(windowCenter) ? windowCenter[0] : windowCenter,
          windowWidth: Array.isArray(windowWidth) ? windowWidth[0] : windowWidth,
          rows,
          columns,
          height: rows,
          width: columns,
          color: samplesPerPixel > 1,
          columnPixelSpacing,
          rowPixelSpacing,
          invert: false,
          sizeInBytes: pixelData.length * (bitsAllocated / 8),
          getPixelData: (frameIndex = 0) => {
            if (frameIndex < frames.length) {
              return frames[frameIndex].pixelData;
            }
            return frames[0].pixelData; // Fallback to first frame
          },
          // Multi-frame properties
          numberOfFrames,
          frames,
          currentFrame: 0,
          frameIncrementPointer,
          // Store original DICOM dataset for metadata access
          dataSet: dataSet
        };
        
        resolve({
          image,
          dataSet,
          metadata: extractMetadataFromDataSet(dataSet)
        });
        
      } catch (error) {
        console.error('Error parsing DICOM file:', error);
        reject(new Error(`Failed to parse DICOM file: ${error.message}`));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsArrayBuffer(file);
  });
};

// 从DataSet提取元数据
const extractMetadataFromDataSet = (dataSet: dicomParser.DataSet) => {
  const getString = (tag: string) => {
    const element = dataSet.elements[tag];
    if (element && element.length > 0) {
      return dataSet.string(tag);
    }
    return '';
  };
  
  const getNumber = (tag: string) => {
    const element = dataSet.elements[tag];
    if (element && element.length > 0) {
      const value = dataSet.string(tag);
      return parseFloat(value) || 0;
    }
    return 0;
  };
  
  return {
    patientName: getString('x00100010'),
    patientId: getString('x00100020'),
    studyDate: getString('x00080020'),
    studyTime: getString('x00080030'),
    studyDescription: getString('x00081030'),
    seriesDescription: getString('x0008103e'),
    modality: getString('x00080060'),
    instanceNumber: getNumber('x00200013'),
    studyInstanceUID: getString('x0020000d'),
    seriesInstanceUID: getString('x0020000e'),
    sopInstanceUID: getString('x00080018'),
    rows: getNumber('x00280010'),
    columns: getNumber('x00280011'),
    pixelSpacing: getString('x00280030'),
    sliceThickness: getNumber('x00180050'),
    windowCenter: getNumber('x00281050'),
    windowWidth: getNumber('x00281051')
  };
};

// Render image to Canvas with improved error handling and viewport management
export const renderImageToCanvas = async (canvas: HTMLCanvasElement, image: CornerstoneImage): Promise<void> => {
  try {
    // Ensure Canvas is properly sized
    const container = canvas.parentElement;
    if (container) {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    }
    
    // Enable canvas if not already enabled
    try {
      cornerstone.getEnabledElement(canvas);
    } catch (error) {
      cornerstone.enable(canvas);
      cornerstone.getEnabledElement(canvas);
    }
    
    // Display image with proper error handling
    await cornerstone.displayImage(canvas, image);
    
    // Set optimized viewport based on image properties
    const viewport = {
      scale: 1.0,
      translation: { x: 0, y: 0 },
      rotation: 0,
      hflip: false,
      vflip: false,
      invert: false,
      pixelReplication: false,
      windowWidth: image.windowWidth || 400,
      windowCenter: image.windowCenter || 40,
      modalityLUT: image.modalityLUT,
      voiLUT: image.voiLUT
    };
    
    // Fit image to canvas while maintaining aspect ratio
    const canvasAspect = canvas.width / canvas.height;
    const imageAspect = image.width / image.height;
    
    if (imageAspect > canvasAspect) {
      // Image is wider than canvas
      viewport.scale = canvas.width / image.width;
    } else {
      // Image is taller than canvas
      viewport.scale = canvas.height / image.height;
    }
    
    // Apply viewport settings
    cornerstone.setViewport(canvas, viewport);
    
    // Force canvas update
    cornerstone.updateImage(canvas);
    
    console.log('Image rendered successfully:', {
      imageSize: `${image.width}x${image.height}`,
      canvasSize: `${canvas.width}x${canvas.height}`,
      scale: viewport.scale,
      windowCenter: viewport.windowCenter,
      windowWidth: viewport.windowWidth
    });
    
  } catch (error) {
    console.error('Failed to render image to canvas:', error);
    throw new Error(`Image rendering failed: ${error.message}`);
  }
};

// Get optimized default viewport for image display
export const getDefaultViewport = (canvas: HTMLCanvasElement, image: CornerstoneImage) => {
  try {
    const enabledElement = cornerstone.getEnabledElement(canvas);
    const defaultViewport = cornerstone.getDefaultViewportForImage(enabledElement.canvas, image);
    
    // Enhance default viewport with image-specific optimizations
    const enhancedViewport = {
      ...defaultViewport,
      windowWidth: image.windowWidth || defaultViewport.windowWidth || 400,
      windowCenter: image.windowCenter || defaultViewport.windowCenter || 40,
      invert: image.invert || false,
      rotation: 0,
      hflip: false,
      vflip: false,
      pixelReplication: false
    };
    
    // Auto-fit image to canvas
    const canvasAspect = canvas.width / canvas.height;
    const imageAspect = image.width / image.height;
    
    if (imageAspect > canvasAspect) {
      enhancedViewport.scale = (canvas.width * 0.9) / image.width;
    } else {
      enhancedViewport.scale = (canvas.height * 0.9) / image.height;
    }
    
    return enhancedViewport;
  } catch (error) {
    console.error('Error getting default viewport:', error);
    // Return fallback viewport
    return {
      scale: 1.0,
      translation: { x: 0, y: 0 },
      rotation: 0,
      hflip: false,
      vflip: false,
      invert: false,
      windowWidth: image.windowWidth || 400,
      windowCenter: image.windowCenter || 40
    };
  }
};

// Apply window level with validation and smooth transitions
export const applyWindowLevel = (canvas: HTMLCanvasElement, windowWidth: number, windowCenter: number) => {
  try {
    const viewport = cornerstone.getViewport(canvas);
    
    // Validate window level values
    const validatedWindowWidth = Math.max(1, Math.abs(windowWidth));
    const validatedWindowCenter = Math.max(-32768, Math.min(32767, windowCenter));
    
    // Apply new window level settings
    viewport.windowWidth = validatedWindowWidth;
    viewport.windowCenter = validatedWindowCenter;
    
    cornerstone.setViewport(canvas, viewport);
    cornerstone.updateImage(canvas);
    
    console.log('Window level applied:', {
      windowWidth: validatedWindowWidth,
      windowCenter: validatedWindowCenter
    });
  } catch (error) {
    console.error('Error applying window level:', error);
    throw new Error(`Failed to apply window level: ${error.message}`);
  }
};

// Navigate to specific frame in multi-frame image
export const navigateToFrame = async (canvas: HTMLCanvasElement, image: CornerstoneImage, frameIndex: number): Promise<void> => {
  try {
    if (!image.frames || frameIndex < 0 || frameIndex >= image.frames.length) {
      throw new Error(`Invalid frame index: ${frameIndex}. Available frames: 0-${image.frames.length - 1}`);
    }
    
    // Update current frame
    image.currentFrame = frameIndex;
    
    // Create new image object with current frame data
    const frameImage = {
      ...image,
      getPixelData: () => image.frames[frameIndex].pixelData
    };
    
    // Re-render with new frame
    await cornerstone.displayImage(canvas, frameImage);
    cornerstone.updateImage(canvas);
    
    console.log(`Navigated to frame ${frameIndex + 1}/${image.frames.length}`);
  } catch (error) {
    console.error('Error navigating to frame:', error);
    throw new Error(`Failed to navigate to frame: ${error.message}`);
  }
};

// Play multi-frame sequence with specified frame rate
export const playFrameSequence = (canvas: HTMLCanvasElement, image: CornerstoneImage, frameRate: number = 10): (() => void) => {
  if (!image.frames || image.frames.length <= 1) {
    console.warn('Cannot play sequence: Image has no multiple frames');
    return () => {};
  }
  
  let currentFrameIndex = image.currentFrame || 0;
  const frameInterval = 1000 / frameRate; // Convert to milliseconds
  
  const intervalId = setInterval(async () => {
    try {
      currentFrameIndex = (currentFrameIndex + 1) % image.frames.length;
      await navigateToFrame(canvas, image, currentFrameIndex);
    } catch (error) {
      console.error('Error during frame playback:', error);
      clearInterval(intervalId);
    }
  }, frameInterval);
  
  // Return stop function
  return () => {
    clearInterval(intervalId);
    console.log('Frame sequence playback stopped');
  };
};

// Get frame information for multi-frame images
export const getFrameInfo = (image: CornerstoneImage) => {
  if (!image.frames) {
    return {
      totalFrames: 1,
      currentFrame: 0,
      hasMultipleFrames: false
    };
  }
  
  return {
    totalFrames: image.frames.length,
    currentFrame: image.currentFrame || 0,
    hasMultipleFrames: image.frames.length > 1,
    frameIncrementPointer: image.frameIncrementPointer,
    frames: image.frames.map((frame, index) => ({
      index,
      frameTime: frame.frameTime
    }))
  };
};

// 缩放图像
export const scaleImage = (canvas: HTMLCanvasElement, scale: number) => {
  const viewport = cornerstone.getViewport(canvas);
  viewport.scale = scale;
  cornerstone.setViewport(canvas, viewport);
};

// 平移图像
export const translateImage = (canvas: HTMLCanvasElement, x: number, y: number) => {
  const viewport = cornerstone.getViewport(canvas);
  viewport.translation.x = x;
  viewport.translation.y = y;
  cornerstone.setViewport(canvas, viewport);
};

// 旋转图像
export const rotateImage = (canvas: HTMLCanvasElement, rotation: number) => {
  const viewport = cornerstone.getViewport(canvas);
  viewport.rotation = (rotation * Math.PI) / 180;
  cornerstone.setViewport(canvas, viewport);
};

// 重置视口
export const resetViewport = (canvas: HTMLCanvasElement, image: CornerstoneImage) => {
  const viewport = cornerstone.getDefaultViewportForImage(canvas, image);
  cornerstone.setViewport(canvas, viewport);
};

// 清理资源
export const cleanup = () => {
  try {
    cornerstoneWADOImageLoader.webWorkerManager.terminate();
  } catch (error) {
    console.error('清理资源失败:', error);
  }
};