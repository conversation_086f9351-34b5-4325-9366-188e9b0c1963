import { DicomFile, DicomSeries, DicomStudy } from '../types/radiology';
import { parseDicomFromFile } from './dicomUtils';

// DICOM序列信息接口
export interface DicomSequenceInfo {
  seriesInstanceUID: string;
  studyInstanceUID: string;
  patientName: string;
  studyDescription: string;
  seriesDescription: string;
  modality: string;
  instanceNumber: number;
  sliceLocation?: number;
  acquisitionTime?: string;
  files: DicomFile[];
}

// 目录扫描结果接口
export interface DicomDirectoryScanResult {
  studies: DicomStudy[];
  series: DicomSeries[];
  totalFiles: number;
  errors: string[];
}

/**
 * 扫描指定目录下的所有DICOM文件
 * @param directoryPath 目录路径（相对于public目录）
 * @returns Promise<DicomDirectoryScanResult>
 */
export async function scanDicomDirectory(directoryPath: string): Promise<DicomDirectoryScanResult> {
  const result: DicomDirectoryScanResult = {
    studies: [],
    series: [],
    totalFiles: 0,
    errors: []
  };

  try {
    // 获取目录下的所有文件
    const files = await fetchDirectoryFiles(directoryPath);
    const dicomFiles: DicomFile[] = [];

    // 处理每个文件
    for (const filePath of files) {
      try {
        if (isDicomFile(filePath)) {
          const response = await fetch(filePath);
          if (response.ok) {
            const arrayBuffer = await response.arrayBuffer();
            const dicomFile = await parseDicomFromFile(new File([arrayBuffer], getFileName(filePath)));
            if (dicomFile) {
              dicomFiles.push(dicomFile);
              result.totalFiles++;
            }
          }
        }
      } catch (error) {
        result.errors.push(`Failed to process file ${filePath}: ${error}`);
      }
    }

    // 按序列组织DICOM文件
    const organizedData = organizeDicomFilesBySeries(dicomFiles);
    result.studies = organizedData.studies;
    result.series = organizedData.series;

  } catch (error) {
    result.errors.push(`Failed to scan directory ${directoryPath}: ${error}`);
  }

  return result;
}

/**
 * 获取目录下的所有文件路径
 * @param directoryPath 目录路径
 * @returns Promise<string[]>
 */
async function fetchDirectoryFiles(directoryPath: string): Promise<string[]> {
  const files: string[] = [];
  
  // 由于浏览器安全限制，我们需要预定义一些已知的DICOM文件路径
  // 这里我们尝试常见的DICOM文件扩展名和编号模式
  const commonExtensions = ['.dcm', '.dicom', '.DCM', '.DICOM'];
  const baseUrl = directoryPath.startsWith('/') ? directoryPath : `/${directoryPath}`;
  
  // 对中文路径进行URL编码处理
  const encodedBaseUrl = baseUrl.split('/').map(segment => {
    // 只对包含中文字符的路径段进行编码
    if (/[\u4e00-\u9fff]/.test(segment)) {
      return encodeURIComponent(segment);
    }
    return segment;
  }).join('/');
  
  // 尝试获取目录索引（如果服务器支持）
  try {
    const response = await fetch(encodedBaseUrl);
    if (response.ok) {
      const html = await response.text();
      // 解析HTML中的文件链接
      const fileLinks = extractFileLinksFromHtml(html, encodedBaseUrl);
      files.push(...fileLinks);
    }
  } catch (error) {
    console.warn('Could not fetch directory listing, trying common patterns:', error);
  }

  // 如果无法获取目录列表，尝试常见的文件命名模式
  if (files.length === 0) {
    for (let i = 1; i <= 1000; i++) {
      for (const ext of commonExtensions) {
        const patterns = [
          `${encodedBaseUrl}/IM${i.toString().padStart(4, '0')}${ext}`,
          `${encodedBaseUrl}/${i.toString().padStart(3, '0')}${ext}`,
          `${encodedBaseUrl}/${i}${ext}`,
          `${encodedBaseUrl}/image${i}${ext}`,
          `${encodedBaseUrl}/slice${i}${ext}`
        ];
        
        for (const pattern of patterns) {
          try {
            const response = await fetch(pattern, { method: 'HEAD' });
            if (response.ok) {
              files.push(pattern);
            }
          } catch {
            // 忽略404错误
          }
        }
      }
      
      // 如果连续10个文件都不存在，停止尝试
      if (i > 10 && files.length === 0) break;
    }
  }

  return files;
}

/**
 * 从HTML中提取文件链接
 * @param html HTML内容
 * @param baseUrl 基础URL
 * @returns string[]
 */
function extractFileLinksFromHtml(html: string, baseUrl: string): string[] {
  const files: string[] = [];
  const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
  let match;

  while ((match = linkRegex.exec(html)) !== null) {
    const href = match[1];
    const filename = match[2];
    
    // 过滤掉目录和非DICOM文件
    if (!href.endsWith('/') && isDicomFile(filename)) {
      const fullPath = href.startsWith('http') ? href : `${baseUrl}/${href}`;
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * 判断是否为DICOM文件
 * @param filename 文件名
 * @returns boolean
 */
function isDicomFile(filename: string): boolean {
  const dicomExtensions = ['.dcm', '.dicom', '.DCM', '.DICOM'];
  return dicomExtensions.some(ext => filename.toLowerCase().endsWith(ext.toLowerCase())) ||
         /^\d+$/.test(filename) || // 纯数字文件名
         filename.toLowerCase().includes('dicom');
}

/**
 * 获取文件名
 * @param filePath 文件路径
 * @returns string
 */
function getFileName(filePath: string): string {
  return filePath.split('/').pop() || filePath;
}

/**
 * 按序列组织DICOM文件
 * @param dicomFiles DICOM文件数组
 * @returns 组织后的研究和序列数据
 */
function organizeDicomFilesBySeries(dicomFiles: DicomFile[]): {
  studies: DicomStudy[];
  series: DicomSeries[];
} {
  const studiesMap = new Map<string, DicomStudy>();
  const seriesMap = new Map<string, DicomSeries>();

  dicomFiles.forEach(file => {
    // 从DICOM元数据中提取信息
    const studyInstanceUID = getMetadataValue(file, '0020000D') || 'unknown-study';
    const seriesInstanceUID = getMetadataValue(file, '0020000E') || 'unknown-series';
    const patientName = getMetadataValue(file, '00100010') || 'Unknown Patient';
    const studyDescription = getMetadataValue(file, '00081030') || 'Unknown Study';
    const seriesDescription = getMetadataValue(file, '0008103E') || 'Unknown Series';
    const modality = getMetadataValue(file, '00080060') || 'Unknown';
    const instanceNumber = parseInt(getMetadataValue(file, '00200013') || '0');
    const sliceLocation = parseFloat(getMetadataValue(file, '00201041') || '0');

    // 创建或更新研究
    if (!studiesMap.has(studyInstanceUID)) {
      studiesMap.set(studyInstanceUID, {
        id: studyInstanceUID,
        patientId: getMetadataValue(file, '00100020') || 'unknown',
        patientName,
        studyDate: getMetadataValue(file, '00080020') || '',
        studyTime: getMetadataValue(file, '00080030') || '',
        studyDescription,
        modality,
        series: []
      });
    }

    // 创建或更新序列
    if (!seriesMap.has(seriesInstanceUID)) {
      const newSeries: DicomSeries = {
        id: seriesInstanceUID,
        studyId: studyInstanceUID,
        seriesNumber: parseInt(getMetadataValue(file, '00200011') || '0'),
        seriesDescription,
        modality,
        instanceCount: 0,
        files: []
      };
      seriesMap.set(seriesInstanceUID, newSeries);
      
      // 添加到对应的研究中
      const study = studiesMap.get(studyInstanceUID);
      if (study) {
        study.series.push(newSeries);
      }
    }

    // 添加文件到序列
    const series = seriesMap.get(seriesInstanceUID);
    if (series) {
      series.files.push(file);
      series.instanceCount = series.files.length;
    }
  });

  // 对每个序列的文件进行排序
  seriesMap.forEach(series => {
    series.files.sort((a, b) => {
      const aInstanceNumber = parseInt(getMetadataValue(a, '00200013') || '0');
      const bInstanceNumber = parseInt(getMetadataValue(b, '00200013') || '0');
      const aSliceLocation = parseFloat(getMetadataValue(a, '00201041') || '0');
      const bSliceLocation = parseFloat(getMetadataValue(b, '00201041') || '0');
      
      // 首先按实例编号排序，然后按切片位置排序
      if (aInstanceNumber !== bInstanceNumber) {
        return aInstanceNumber - bInstanceNumber;
      }
      return aSliceLocation - bSliceLocation;
    });
  });

  return {
    studies: Array.from(studiesMap.values()),
    series: Array.from(seriesMap.values())
  };
}

/**
 * 从DICOM文件元数据中获取值
 * @param file DICOM文件
 * @param tag DICOM标签
 * @returns string | undefined
 */
function getMetadataValue(file: DicomFile, tag: string): string | undefined {
  try {
    if (file.metadata && typeof file.metadata === 'object') {
      // 尝试不同的元数据访问方式
      const metadata = file.metadata as any;
      
      // 直接访问标签
      if (metadata[tag]) {
        return String(metadata[tag].Value || metadata[tag]);
      }
      
      // 尝试访问嵌套的元数据结构
      if (metadata.elements && metadata.elements[tag]) {
        return String(metadata.elements[tag].Value || metadata.elements[tag]);
      }
      
      // 尝试访问数据集
      if (metadata.dataset && metadata.dataset[tag]) {
        return String(metadata.dataset[tag].Value || metadata.dataset[tag]);
      }
    }
  } catch (error) {
    console.warn(`Failed to get metadata value for tag ${tag}:`, error);
  }
  
  return undefined;
}

/**
 * 获取预定义的DICOM目录列表
 * @returns 可用的DICOM目录路径数组
 */
export function getAvailableDicomDirectories(): string[] {
  return [
    '/dicom',
    '/dicom/肋骨',
    '/dicom/骨折',
    '/dicom/PCT001191'
  ];
}

/**
 * 加载序列中的所有DICOM文件到DWV查看器
 * @param series DICOM序列
 * @returns Promise<string[]> 文件URL数组
 */
export async function loadSeriesFiles(series: DicomSeries): Promise<string[]> {
  const fileUrls: string[] = [];
  
  for (const file of series.files) {
    try {
      // 如果文件有URL，直接使用
      if (file.url) {
        fileUrls.push(file.url);
      } else if (file.file) {
        // 如果是File对象，创建临时URL
        const url = URL.createObjectURL(file.file);
        fileUrls.push(url);
      }
    } catch (error) {
      console.warn('Failed to load file:', error);
    }
  }
  
  return fileUrls;
}