// A minimal, reliable source for DICOM file URLs under public/dicom.
// Prefers a manifest file, with a graceful numeric-pattern fallback.

export interface DicomSequence {
  id: string;
  name?: string;
  path: string; // relative to /dicom (e.g. "/肋骨" or "/PCT001191/CT")
  files?: string[]; // file names in the sequence folder (e.g. ["1.dcm", "2.dcm"]) 
}

export interface DicomManifest {
  sequences: DicomSequence[];
}

// Encode each path segment to safely handle Chinese or special chars
function encodePathSegments(p: string): string {
  return p
    .split('/')
    .map((seg) => (seg && /[\u4e00-\u9fff]/.test(seg) ? encodeURIComponent(seg) : seg))
    .join('/');
}

export async function loadManifest(base = '/dicom'): Promise<DicomManifest | null> {
  const url = `${base.replace(/\/$/, '')}/manifest.json?_=${Date.now()}`;
  try {
    const res = await fetch(url);
    if (!res.ok) return null;
    const data = (await res.json()) as DicomManifest;
    if (!data || !Array.isArray(data.sequences)) return null;
    return data;
  } catch {
    return null;
  }
}

// Build absolute URLs from a sequence and file names
export function buildFileUrls(base: string, seqPath: string, files: string[]): string[] {
  const root = base.replace(/\/$/, '');
  const encoded = encodePathSegments(seqPath);
  return files.map((f) => `${root}${encoded}/${encodeURIComponent(f)}`);
}

// Lightweight numeric-pattern probe for environments without a manifest
// Tries a small, fast subset of common names to keep latency acceptable.
export async function probeSequence(base: string, seqPath: string): Promise<string[]> {
  const candidates: string[] = [];
  const patterns = (
    idx: number,
  ) => [
    `IM${String(idx).padStart(4, '0')}.dcm`,
    `${String(idx).padStart(3, '0')}.dcm`,
    `${idx}.dcm`,
  ];

  const encodedSeq = encodePathSegments(seqPath);
  const root = base.replace(/\/$/, '');

  // Try up to 200 frames fast; stop early after a run of misses
  let misses = 0;
  const maxMisses = 25;
  for (let i = 1; i <= 200; i++) {
    let found = false;
    for (const name of patterns(i)) {
      const url = `${root}${encodedSeq}/${name}`;
      try {
        const head = await fetch(url, { method: 'HEAD' });
        if (head.ok) {
          candidates.push(url);
          found = true;
          break;
        }
      } catch {
        // ignore
      }
    }
    if (!found) {
      misses++;
      if (i > 10 && misses >= maxMisses) break;
    } else {
      misses = 0;
    }
  }

  return candidates;
}

export async function getAvailableSequences(base = '/dicom'): Promise<DicomSequence[]> {
  const manifest = await loadManifest(base);
  if (manifest) return manifest.sequences;

  // Fallback: offer commonly used demo folders if present in docs
  // Users can still type or paste a custom subpath in the viewer UI.
  return [
    { id: 'rib', name: '肋骨', path: '/肋骨' },
    { id: 'pct-sample', name: 'PCT001191', path: '/PCT001191' },
  ];
}

export async function getSequenceUrls(base: string, seq: DicomSequence): Promise<string[]> {
  if (seq.files?.length) return buildFileUrls(base, seq.path, seq.files);
  return probeSequence(base, seq.path);
}

