# DICOM目录上传功能测试说明

## 修复内容

1. **目录上传支持**: 将文件上传改为支持目录上传，使用 `webkitdirectory` 属性
2. **错误修复**: 修复了 `processUploadedFiles` 函数调用时传递错误参数的问题
3. **React警告修复**: 正确设置 `webkitdirectory` 属性以避免React警告
4. **调试信息**: 添加了控制台日志来跟踪上传的文件信息

## 测试步骤

1. 打开应用程序: http://localhost:9527
2. 导航到 DICOM Viewer 页面
3. 点击 "Upload DICOM Directory" 按钮
4. 在文件选择对话框中，选择一个包含DICOM文件的目录（如 `public/dicom/PCT001191` 或 `public/dicom/肋骨`）
5. 观察控制台输出，应该显示:
   - 选择的文件数量
   - 每个文件的相对路径
6. 验证DICOM文件是否正确加载和显示

## 预期行为

- 用户现在可以选择整个DICOM目录而不是单个文件
- 系统会自动处理目录中的所有DICOM文件
- 文件会按照研究(Study)和序列(Series)进行组织
- 控制台会显示详细的文件信息用于调试

## 技术细节

- 使用 `webkitdirectory` 属性启用目录选择
- 通过 `file.webkitRelativePath` 获取文件的相对路径
- 修复了函数调用参数不匹配的问题
- 添加了适当的错误处理和用户反馈