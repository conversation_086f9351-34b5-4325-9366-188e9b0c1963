<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Trae Project</title>
    <script type="module">
      if (import.meta.hot?.on) {
        import.meta.hot.on('vite:error', (error) => {
          if (error.err) {
            console.error(
              [error.err.message, error.err.frame].filter(Boolean).join('\n'),
            )
          }
        })
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
