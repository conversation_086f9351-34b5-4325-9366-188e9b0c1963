{"name": "medical-integration-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "check": "tsc -b --noEmit"}, "dependencies": {"@cornerstonejs/core": "^4.3.14", "@cornerstonejs/dicom-image-loader": "^4.3.14", "@cornerstonejs/streaming-image-volume-loader": "^1.86.1", "@cornerstonejs/tools": "^4.3.14", "clsx": "^2.1.1", "cornerstone-core": "^2.6.1", "cornerstone-wado-image-loader": "^4.13.2", "dicom-parser": "^1.8.21", "dwv": "^0.35.1", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dicom-viewer": "^0.0.10", "react-dom": "^18.3.1", "react-router-dom": "^7.3.0", "sonner": "^1.7.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "babel-plugin-react-dev-locator": "^1.0.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-trae-solo-badge": "^1.0.0", "vite-tsconfig-paths": "^5.1.4"}}