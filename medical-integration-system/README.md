# 医疗信息系统整合平台

一个现代化的医疗信息系统整合平台，集成了临床工作台和医学影像浏览功能，为医疗机构提供高效的患者信息管理和医学影像诊断支持。

## 🌟 项目特色

- **🏥 临床工作台**：完整的患者信息管理系统，支持病历录入、诊断信息管理
- **🖼️ DICOM影像浏览**：专业的医学影像浏览器，支持CT、PT等多种影像格式
- **📱 响应式设计**：适配不同屏幕尺寸，支持桌面端和移动端
- **⚡ 高性能**：基于React 18和Vite构建，提供快速的开发和运行体验
- **🎨 现代UI**：采用Tailwind CSS设计，界面简洁美观

## 🚀 功能模块

### 临床工作台
- **患者列表管理**：支持患者信息的查看、搜索和筛选
- **病历信息录入**：包含基本信息、主诉、现病史、既往史等完整病历结构
- **诊断信息管理**：支持诊断结果的录入和管理
- **医嘱录入**：完整的医嘱管理系统
- **三栏布局**：优化的工作流程，提高医生工作效率

### 医学影像浏览
- **DICOM文件支持**：原生支持DICOM格式医学影像
- **多模态影像**：支持CT、PT、MRI等多种影像类型
- **影像操作工具**：缩放、旋转、窗宽窗位调节等专业功能
- **序列浏览**：支持影像序列的快速浏览和切换
- **测量工具**：提供基础的影像测量功能

## 🛠️ 技术栈

### 前端框架
- **React 18** - 现代化的前端框架
- **TypeScript** - 类型安全的JavaScript超集
- **Vite** - 快速的构建工具和开发服务器

### UI组件库
- **Tailwind CSS** - 实用优先的CSS框架
- **Lucide React** - 现代化的图标库
- **Sonner** - 优雅的通知组件

### 状态管理
- **Zustand** - 轻量级状态管理库
- **React Router DOM** - 客户端路由管理

### 医学影像处理
- **DICOM Parser** - DICOM文件解析库
- **Cornerstone Core** - 医学影像显示引擎

### 开发工具
- **ESLint** - 代码质量检查
- **PostCSS** - CSS后处理器
- **TypeScript ESLint** - TypeScript代码规范

## 📦 安装与运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0 或 pnpm >= 6.0.0

### 安装依赖
```bash
# 使用npm
npm install

# 或使用pnpm（推荐）
pnpm install
```

### 开发环境运行
```bash
# 启动开发服务器
npm run dev

# 或使用pnpm
pnpm dev
```

开发服务器将在 `http://localhost:9527` 启动

### 生产环境构建
```bash
# 构建生产版本
npm run build

# 或使用pnpm
pnpm build
```

构建产物将生成在 `dist/` 目录中

### 预览生产构建
```bash
# 预览生产构建
npm run preview

# 或使用pnpm
pnpm preview
```

## 📁 项目结构

```
medical-integration-system/
├── public/                 # 静态资源
│   ├── dicom/             # DICOM示例文件
│   └── favicon.svg        # 网站图标
├── src/                   # 源代码
│   ├── components/        # React组件
│   │   ├── ClinicalWorkspace.tsx    # 临床工作台
│   │   ├── RadiologyWorkspace.tsx   # 影像工作台
│   │   └── Empty.tsx               # 空状态组件
│   ├── pages/            # 页面组件
│   │   ├── Home.tsx      # 首页
│   │   └── DicomViewer.tsx # DICOM浏览器
│   ├── store/            # 状态管理
│   │   └── useRadiologyStore.ts
│   ├── types/            # TypeScript类型定义
│   │   └── radiology.ts
│   ├── utils/            # 工具函数
│   │   └── dicomUtils.ts # DICOM处理工具
│   ├── data/             # 数据文件
│   │   └── radiologyData.ts
│   ├── hooks/            # 自定义Hooks
│   │   └── useTheme.ts
│   ├── lib/              # 库文件
│   │   └── utils.ts
│   └── assets/           # 资源文件
├── dist/                 # 构建产物
├── package.json          # 项目配置
├── vite.config.ts        # Vite配置
├── tailwind.config.js    # Tailwind配置
├── tsconfig.json         # TypeScript配置
└── README.md            # 项目文档
```

## 🔧 配置说明

### 端口配置
项目默认运行在端口 `9527`，可在 `vite.config.ts` 中修改：

```typescript
export default defineConfig({
  server: {
    port: 9527, // 修改此处更改端口
  },
  // ...其他配置
})
```

### DICOM数据配置
DICOM影像数据路径配置在 `src/utils/dicomUtils.ts` 中：

```typescript
// 扫描DICOM目录
const dicomPath = '/path/to/your/dicom/directory';
```

## 🚀 部署指南

### 静态部署
1. 构建项目：`npm run build`
2. 将 `dist/` 目录部署到静态服务器（如Nginx、Apache等）

### Vercel部署
项目已配置Vercel部署，直接推送到Git仓库即可自动部署。

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 9527
CMD ["npm", "run", "preview"]
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支：`git checkout -b feature/AmazingFeature`
3. 提交更改：`git commit -m 'Add some AmazingFeature'`
4. 推送到分支：`git push origin feature/AmazingFeature`
5. 提交Pull Request

## 📝 开发规范

### 代码规范
- 使用TypeScript进行类型安全开发
- 遵循ESLint配置的代码规范
- 组件命名使用PascalCase
- 文件命名使用camelCase

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 🐛 问题反馈

如果您在使用过程中遇到问题，请通过以下方式反馈：

1. 在GitHub Issues中提交问题
2. 提供详细的问题描述和复现步骤
3. 包含相关的错误日志和环境信息

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

感谢以下开源项目的支持：
- [React](https://reactjs.org/)
- [Vite](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [DICOM Parser](https://github.com/cornerstonejs/dicomParser)
- [Cornerstone](https://cornerstonejs.org/)

---

**医疗信息系统整合平台** - 为医疗机构提供现代化的信息管理解决方案
