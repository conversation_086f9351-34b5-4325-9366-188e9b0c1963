import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from "vite-tsconfig-paths";
import { traeBadgePlugin } from 'vite-plugin-trae-solo-badge';

// https://vite.dev/config/
export default defineConfig({
  server: {
    port: 9527,
    host: true,
    fs: {
      allow: ['..']
    }
  },
  build: {
    sourcemap: 'hidden',
  },
  optimizeDeps: {
    include: [
      '@cornerstonejs/core',
      '@cornerstonejs/tools', 
      'cornerstone-core',
      'cornerstone-wado-image-loader',
      'dicom-parser'
    ],
    exclude: ['@cornerstonejs/dicom-image-loader', 'decodeImageFrameWorker.js']
  },
  worker: {
    format: 'es',
    plugins: [],
    rollupOptions: {
      external: ['comlink']
    }
  },
  assetsInclude: ['**/*.wasm', '**/*Worker.js'],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    global: 'globalThis'
  },
  plugins: [
    react({
      babel: {
        plugins: [
          'react-dev-locator',
        ],
      },
    }),
    traeBadgePlugin({
      variant: 'dark',
      position: 'bottom-right',
      prodOnly: true,
      clickable: true,
      clickUrl: 'https://www.trae.ai/solo?showJoin=1',
      autoTheme: true,
      autoThemeTarget: '#root'
    }), 
    tsconfigPaths()
  ],
})
