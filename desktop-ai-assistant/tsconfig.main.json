{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist/main", "target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": true, "types": ["node", "electron"]}, "include": ["src/main/**/*", "src/shared/**/*"], "exclude": ["node_modules", "dist", "build", "release", "src/renderer", "tests", "**/*.test.ts", "**/*.spec.ts"]}