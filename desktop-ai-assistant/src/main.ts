import { app, BrowserWindow, ipcMain, globalShortcut, Tray, Menu, nativeImage, desktopCapturer, screen } from 'electron';
import * as path from 'path';
import { ConfigService } from './services/config';
import { VoiceService } from './services/voice';
import { AIService } from './services/ai';
import { MedicalIntegrationService } from './services/medical-integration';
import { Logger } from './utils/logger';
import type { 
  AppStatus, 
  WindowMode, 
  AppConfig, 
  VoiceConfig, 
  AIConfig, 
  MedicalConfig,
  ScreenCaptureOptions,
  NotificationOptions
} from './shared/types';

/**
 * 桌面AI助手主应用类
 */
class DesktopAIAssistant {
  private logger: Logger;
  private configService: ConfigService;
  private voiceService: VoiceService;
  private aiService: AIService;
  private medicalService: MedicalIntegrationService;
  
  private mainWindow: BrowserWindow | null = null;
  private floatingWindow: BrowserWindow | null = null;
  private voiceInputWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  
  private appStatus: AppStatus = 'initializing';
  private isQuitting = false;
  private isDevelopment = process.env.NODE_ENV === 'development';

  constructor() {
    this.logger = new Logger();
    this.configService = new ConfigService(this.logger);
    
    // 获取配置后再初始化其他服务
    const config = this.configService.getConfig();
    this.voiceService = new VoiceService(config.voice, this.logger);
    this.aiService = new AIService(config.ai, this.logger);
    this.medicalService = new MedicalIntegrationService(config.medical, this.logger);
    
    this.setupEventHandlers();
  }

  /**
   * 初始化应用
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Desktop AI Assistant...');
      this.appStatus = 'initializing';
      
      // 初始化服务
      await this.configService.initialize();
      await this.voiceService.initialize();
      await this.aiService.initialize();
      await this.medicalService.initialize();
      
      // 创建系统托盘
      this.createTray();
      
      // 注册全局快捷键
      this.registerGlobalShortcuts();
      
      // 设置IPC处理器
      this.setupIPCHandlers();
      
      // 创建主窗口（隐藏状态）
      await this.createMainWindow();
      
      this.appStatus = 'ready';
      this.logger.info('Desktop AI Assistant initialized successfully');
      
      // 根据配置决定是否显示浮动窗口
      const config = this.configService.getConfig();
      if (config.startup.showFloatingWindow) {
        this.showFloatingWindow();
      }
      
      // 在开发模式下默认显示主窗口
      if (this.isDevelopment) {
        this.showMainWindow();
      }
    } catch (error) {
      this.logger.error('Failed to initialize application:', error);
      this.appStatus = 'error';
      throw error;
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 应用事件
    app.on('ready', () => this.initialize());
    app.on('window-all-closed', this.onWindowAllClosed.bind(this));
    app.on('activate', this.onActivate.bind(this));
    app.on('before-quit', this.onBeforeQuit.bind(this));
    app.on('will-quit', this.onWillQuit.bind(this));
    
    // 服务事件
    this.voiceService.on('stateChanged', (state) => {
      this.broadcastToWindows('voice-state-changed', state);
    });
    
    this.aiService.on('stateChanged', (state) => {
      this.broadcastToWindows('ai-state-changed', state);
    });
    
    this.medicalService.on('stateChanged', (state) => {
      this.broadcastToWindows('medical-state-changed', state);
    });
  }

  /**
   * 创建主窗口
   */
  private async createMainWindow(): Promise<void> {
    const config = this.configService.getConfig();
    
    this.mainWindow = new BrowserWindow({
      width: config.windows.main.width,
      height: config.windows.main.height,
      minWidth: 800,
      minHeight: 600,
      show: false,
      frame: true,
      titleBarStyle: 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      }
    });
    
    // 加载页面
    const url = this.isDevelopment
      ? 'http://localhost:3000?mode=main'
      : `file://${path.join(__dirname, '../renderer/index.html')}?mode=main`;
    
    await this.mainWindow.loadURL(url);
    
    // 开发模式下打开开发者工具
    if (this.isDevelopment) {
      this.mainWindow.webContents.openDevTools();
    }
    
    // 窗口事件
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
    
    this.mainWindow.on('close', (event) => {
      if (!this.isQuitting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });
    
    this.logger.info('Main window created');
  }

  /**
   * 创建浮动窗口
   */
  private async createFloatingWindow(): Promise<void> {
    if (this.floatingWindow) {
      return;
    }
    
    const config = this.configService.getConfig();
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
    
    this.floatingWindow = new BrowserWindow({
      width: config.windows.floating.width,
      height: config.windows.floating.height,
      x: screenWidth - config.windows.floating.width - 20,
      y: 20,
      show: false,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      movable: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      }
    });
    
    // 加载页面
    const url = this.isDevelopment
      ? 'http://localhost:3000?mode=floating'
      : `file://${path.join(__dirname, '../renderer/index.html')}?mode=floating`;
    
    await this.floatingWindow.loadURL(url);
    
    // 窗口事件
    this.floatingWindow.on('closed', () => {
      this.floatingWindow = null;
    });
    
    this.logger.info('Floating window created');
  }

  /**
   * 创建语音输入窗口
   */
  private async createVoiceInputWindow(): Promise<void> {
    if (this.voiceInputWindow) {
      return;
    }
    
    const config = this.configService.getConfig();
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
    
    this.voiceInputWindow = new BrowserWindow({
      width: config.windows.voice.width,
      height: config.windows.voice.height,
      x: Math.floor((screenWidth - config.windows.voice.width) / 2),
      y: Math.floor((screenHeight - config.windows.voice.height) / 2),
      show: false,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      movable: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      }
    });
    
    // 加载页面
    const url = this.isDevelopment
      ? 'http://localhost:3000?mode=voice'
      : `file://${path.join(__dirname, '../renderer/index.html')}?mode=voice`;
    
    await this.voiceInputWindow.loadURL(url);
    
    // 窗口事件
    this.voiceInputWindow.on('closed', () => {
      this.voiceInputWindow = null;
    });
    
    this.logger.info('Voice input window created');
  }

  /**
   * 创建系统托盘
   */
  private createTray(): void {
    const iconPath = path.join(__dirname, '../assets/tray-icon.png');
    const trayIcon = nativeImage.createFromPath(iconPath);
    
    this.tray = new Tray(trayIcon);
    this.tray.setToolTip('桌面AI助手');
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主界面',
        click: () => this.showMainWindow()
      },
      {
        label: '浮动窗口',
        type: 'checkbox',
        checked: this.floatingWindow?.isVisible() || false,
        click: () => this.toggleFloatingWindow()
      },
      { type: 'separator' },
      {
        label: '语音输入',
        accelerator: 'CmdOrCtrl+Shift+V',
        click: () => this.toggleVoiceInput()
      },
      {
        label: '屏幕截图',
        accelerator: 'CmdOrCtrl+Shift+S',
        click: () => this.captureScreen()
      },
      { type: 'separator' },
      {
        label: '设置',
        click: () => this.showSettings()
      },
      {
        label: '关于',
        click: () => this.showAbout()
      },
      { type: 'separator' },
      {
        label: '退出',
        click: () => this.quit()
      }
    ]);
    
    this.tray.setContextMenu(contextMenu);
    
    // 双击托盘图标显示主窗口
    this.tray.on('double-click', () => {
      this.showMainWindow();
    });
    
    this.logger.info('System tray created');
  }

  /**
   * 注册全局快捷键
   */
  private registerGlobalShortcuts(): void {
    const config = this.configService.getConfig();
    
    try {
      // 显示/隐藏主窗口
      globalShortcut.register(config.shortcuts.toggleMainWindow, () => {
        this.toggleMainWindow();
      });
      
      // 显示/隐藏浮动窗口
      globalShortcut.register(config.shortcuts.toggleFloatingWindow, () => {
        this.toggleFloatingWindow();
      });
      
      // 语音输入
      globalShortcut.register(config.shortcuts.voiceInput, () => {
        this.toggleVoiceInput();
      });
      
      // 屏幕截图
      globalShortcut.register(config.shortcuts.screenCapture, () => {
        this.captureScreen();
      });
      
      // 快速搜索
      globalShortcut.register(config.shortcuts.quickSearch, () => {
        this.showQuickSearch();
      });
      
      this.logger.info('Global shortcuts registered');
    } catch (error) {
      this.logger.error('Failed to register global shortcuts:', error);
    }
  }

  /**
   * 设置IPC处理器
   */
  private setupIPCHandlers(): void {
    // 窗口控制
    ipcMain.handle('window-show', (event, mode: WindowMode) => {
      return this.showWindow(mode);
    });
    
    ipcMain.handle('window-hide', (event, mode: WindowMode) => {
      return this.hideWindow(mode);
    });
    
    ipcMain.handle('window-toggle', (event, mode: WindowMode) => {
      return this.toggleWindow(mode);
    });
    
    ipcMain.handle('window-close', (event, mode: WindowMode) => {
      return this.closeWindow(mode);
    });
    
    // 应用状态
    ipcMain.handle('app-get-status', () => {
      return this.appStatus;
    });
    
    ipcMain.handle('app-quit', () => {
      return this.quit();
    });
    
    // 配置管理
    ipcMain.handle('config-get', () => {
      return this.configService.getConfig();
    });
    
    ipcMain.handle('config-set', (event, config: Partial<AppConfig>) => {
      return this.configService.updateConfig(config);
    });
    
    ipcMain.handle('config-reset', () => {
      return this.configService.resetToDefaults();
    });
    
    // 语音服务
    ipcMain.handle('voice-start-listening', () => {
      return this.voiceService.startListening();
    });
    
    ipcMain.handle('voice-stop-listening', () => {
      return this.voiceService.stopListening();
    });
    
    ipcMain.handle('voice-get-state', () => {
      return this.voiceService.getState();
    });
    
    ipcMain.handle('voice-update-config', (event, config: Partial<VoiceConfig>) => {
      return this.voiceService.updateConfig(config);
    });
    
    // AI服务
    ipcMain.handle('ai-send-message', (event, message: string, context?: any) => {
      return this.aiService.sendMessage(message, context);
    });
    
    ipcMain.handle('ai-get-suggestions', (event, context?: any) => {
      return this.aiService.generateSuggestions(context);
    });
    
    ipcMain.handle('ai-update-config', (event, config: Partial<AIConfig>) => {
      return this.aiService.updateConfig(config);
    });
    
    // 医疗集成
    ipcMain.handle('medical-search-patients', (event, query: string) => {
      return this.medicalService.searchPatients({ query });
    });
    
    ipcMain.handle('medical-get-patient', (event, patientId: string) => {
      return this.medicalService.getPatientDetails(patientId);
    });
    
    ipcMain.handle('medical-update-config', (event, config: Partial<MedicalConfig>) => {
      return this.medicalService.updateConfig(config);
    });
    
    // 屏幕截图
    ipcMain.handle('screen-capture', (event, options?: ScreenCaptureOptions) => {
      return this.captureScreen(options);
    });
    
    // 通知
    ipcMain.handle('notification-show', (event, options: NotificationOptions) => {
      return this.showNotification(options);
    });
    
    this.logger.info('IPC handlers setup completed');
  }

  /**
   * 向所有窗口广播消息
   */
  private broadcastToWindows(channel: string, data: any): void {
    const windows = [this.mainWindow, this.floatingWindow, this.voiceInputWindow];
    
    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        window.webContents.send(channel, data);
      }
    });
  }

  /**
   * 显示窗口
   */
  private async showWindow(mode: WindowMode): Promise<void> {
    switch (mode) {
      case 'main':
        await this.showMainWindow();
        break;
      case 'floating':
        await this.showFloatingWindow();
        break;
      case 'voice':
        await this.showVoiceInputWindow();
        break;
    }
  }

  /**
   * 隐藏窗口
   */
  private hideWindow(mode: WindowMode): void {
    switch (mode) {
      case 'main':
        this.mainWindow?.hide();
        break;
      case 'floating':
        this.floatingWindow?.hide();
        break;
      case 'voice':
        this.voiceInputWindow?.hide();
        break;
    }
  }

  /**
   * 切换窗口显示状态
   */
  private async toggleWindow(mode: WindowMode): Promise<void> {
    switch (mode) {
      case 'main':
        await this.toggleMainWindow();
        break;
      case 'floating':
        await this.toggleFloatingWindow();
        break;
      case 'voice':
        await this.toggleVoiceInput();
        break;
    }
  }

  /**
   * 关闭窗口
   */
  private closeWindow(mode: WindowMode): void {
    switch (mode) {
      case 'main':
        this.mainWindow?.close();
        break;
      case 'floating':
        this.floatingWindow?.close();
        break;
      case 'voice':
        this.voiceInputWindow?.close();
        break;
    }
  }

  /**
   * 显示主窗口
   */
  private async showMainWindow(): Promise<void> {
    if (!this.mainWindow) {
      await this.createMainWindow();
    }
    
    this.mainWindow?.show();
    this.mainWindow?.focus();
  }

  /**
   * 切换主窗口
   */
  private async toggleMainWindow(): Promise<void> {
    if (!this.mainWindow) {
      await this.showMainWindow();
      return;
    }
    
    if (this.mainWindow.isVisible()) {
      this.mainWindow.hide();
    } else {
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  /**
   * 显示浮动窗口
   */
  private async showFloatingWindow(): Promise<void> {
    if (!this.floatingWindow) {
      await this.createFloatingWindow();
    }
    
    this.floatingWindow?.show();
  }

  /**
   * 切换浮动窗口
   */
  private async toggleFloatingWindow(): Promise<void> {
    if (!this.floatingWindow) {
      await this.showFloatingWindow();
      return;
    }
    
    if (this.floatingWindow.isVisible()) {
      this.floatingWindow.hide();
    } else {
      this.floatingWindow.show();
    }
  }

  /**
   * 显示语音输入窗口
   */
  private async showVoiceInputWindow(): Promise<void> {
    if (!this.voiceInputWindow) {
      await this.createVoiceInputWindow();
    }
    
    this.voiceInputWindow?.show();
    this.voiceInputWindow?.focus();
  }

  /**
   * 切换语音输入
   */
  private async toggleVoiceInput(): Promise<void> {
    if (!this.voiceInputWindow) {
      await this.showVoiceInputWindow();
      return;
    }
    
    if (this.voiceInputWindow.isVisible()) {
      this.voiceInputWindow.hide();
      this.voiceService.stopListening();
    } else {
      this.voiceInputWindow.show();
      this.voiceInputWindow.focus();
      this.voiceService.startListening();
    }
  }

  /**
   * 屏幕截图
   */
  private async captureScreen(options?: ScreenCaptureOptions): Promise<string | null> {
    try {
      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: { width: 1920, height: 1080 }
      });
      
      if (sources.length === 0) {
        throw new Error('No screen sources available');
      }
      
      const primarySource = sources[0];
      const dataURL = primarySource.thumbnail.toDataURL();
      
      this.logger.info('Screen captured successfully');
      return dataURL;
    } catch (error) {
      this.logger.error('Failed to capture screen:', error);
      return null;
    }
  }

  /**
   * 显示快速搜索
   */
  private async showQuickSearch(): Promise<void> {
    await this.showMainWindow();
    this.mainWindow?.webContents.send('show-quick-search');
  }

  /**
   * 显示设置
   */
  private async showSettings(): Promise<void> {
    await this.showMainWindow();
    this.mainWindow?.webContents.send('show-settings');
  }

  /**
   * 显示关于
   */
  private async showAbout(): Promise<void> {
    await this.showMainWindow();
    this.mainWindow?.webContents.send('show-about');
  }

  /**
   * 显示通知
   */
  private showNotification(options: NotificationOptions): void {
    // 这里可以使用系统通知或自定义通知
    this.logger.info('Notification:', options);
  }

  /**
   * 应用事件处理器
   */
  private onWindowAllClosed(): void {
    // 桌面AI助手是一个后台应用，即使所有窗口都关闭也应该保持运行
    // 用户可以通过系统托盘或全局快捷键重新打开窗口
    // 只有在明确退出时才关闭应用
    this.logger.info('All windows closed, but keeping app running in background');
  }

  private async onActivate(): Promise<void> {
    // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开的时候，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) {
      await this.createMainWindow();
    }
  }

  private onBeforeQuit(): void {
    this.isQuitting = true;
  }

  private onWillQuit(event: Electron.Event): void {
    // 注销全局快捷键
    globalShortcut.unregisterAll();
    
    // 清理服务
    this.cleanup();
  }

  /**
   * 退出应用
   */
  private quit(): void {
    this.isQuitting = true;
    app.quit();
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    try {
      this.logger.info('Cleaning up application resources...');
      
      // 清理服务
      await this.configService.cleanup();
      await this.voiceService.cleanup();
      await this.aiService.cleanup();
      await this.medicalService.cleanup();
      
      // 清理日志
      await this.logger.cleanup();
      
      this.logger.info('Application cleanup completed');
    } catch (error) {
      console.error('Failed to cleanup application:', error);
    }
  }
}

// 创建应用实例
const app_instance = new DesktopAIAssistant();

// 导出应用实例（用于测试）
export default app_instance;