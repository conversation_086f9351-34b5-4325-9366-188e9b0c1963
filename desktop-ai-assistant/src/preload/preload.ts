import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { AppStatus, ChatMessage, AnalysisRequest, SearchFilters } from '../shared/types';

// 定义暴露给渲染进程的API接口
interface ElectronAPI {
  // 窗口控制
  window: {
    minimize: () => Promise<void>;
    close: () => Promise<void>;
    toggleAlwaysOnTop: () => Promise<boolean>;
  };
  
  // 应用状态
  app: {
    getStatus: () => Promise<AppStatus>;
    onStatusChange: (callback: (status: AppStatus) => void) => void;
    removeStatusListener: (callback: (status: AppStatus) => void) => void;
  };
  
  // 配置管理
  config: {
    get: (path?: string) => Promise<any>;
    set: (path: string, value: any) => Promise<boolean>;
    onChange: (callback: (path: string, value: any) => void) => void;
    removeConfigListener: (callback: (path: string, value: any) => void) => void;
  };
  
  // 语音服务
  voice: {
    startListening: () => Promise<boolean>;
    stopListening: () => Promise<boolean>;
    speak: (text: string) => Promise<boolean>;
    onStateChange: (callback: (state: any) => void) => void;
    onRecognitionResult: (callback: (result: any) => void) => void;
    removeVoiceListener: (callback: (state: any) => void) => void;
  };
  
  // AI服务
  ai: {
    chat: (messages: ChatMessage[]) => Promise<any>;
    analyzeContent: (request: AnalysisRequest) => Promise<any>;
    onSuggestion: (callback: (suggestion: any) => void) => void;
    removeSuggestionListener: (callback: (suggestion: any) => void) => void;
  };
  
  // 医疗集成
  medical: {
    search: (filters: SearchFilters) => Promise<any>;
    getPatient: (id: string) => Promise<any>;
    getStudy: (id: string) => Promise<any>;
    onDataUpdate: (callback: (data: any) => void) => void;
    removeMedicalListener: (callback: (data: any) => void) => void;
  };
  
  // 截屏功能
  screen: {
    capture: () => Promise<string>;
    onScreenCaptured: (callback: (data: any) => void) => void;
    removeScreenListener: (callback: (data: any) => void) => void;
  };
  
  // 窗口显示控制
  windows: {
    showFloating: () => Promise<void>;
    hideFloating: () => Promise<void>;
    showVoiceInput: () => Promise<void>;
    hideVoiceInput: () => Promise<void>;
  };
  
  // 通知系统
  notifications: {
    show: (notification: any) => Promise<void>;
    onNotification: (callback: (notification: any) => void) => void;
    removeNotificationListener: (callback: (notification: any) => void) => void;
  };
  
  // 事件系统
  events: {
    on: (event: string, callback: (...args: any[]) => void) => void;
    off: (event: string, callback: (...args: any[]) => void) => void;
    emit: (event: string, ...args: any[]) => void;
  };
  
  // 系统信息
  system: {
    getInfo: () => Promise<any>;
    getPerformance: () => Promise<any>;
  };
  
  // 开发工具（仅在开发模式下可用）
  dev?: {
    openDevTools: () => Promise<void>;
    reload: () => Promise<void>;
    toggleDevTools: () => Promise<void>;
  };
}

// 实现API
const electronAPI: ElectronAPI = {
  // 窗口控制
  window: {
    minimize: () => ipcRenderer.invoke('window-minimize'),
    close: () => ipcRenderer.invoke('window-close'),
    toggleAlwaysOnTop: () => ipcRenderer.invoke('window-toggle-always-on-top'),
  },
  
  // 应用状态
  app: {
    getStatus: () => ipcRenderer.invoke('get-app-status'),
    onStatusChange: (callback) => {
      ipcRenderer.on('app-status-changed', (_, status) => callback(status));
    },
    removeStatusListener: (callback) => {
      ipcRenderer.removeListener('app-status-changed', callback);
    },
  },
  
  // 配置管理
  config: {
    get: (path) => ipcRenderer.invoke('get-config', path),
    set: (path, value) => ipcRenderer.invoke('set-config', path, value),
    onChange: (callback) => {
      ipcRenderer.on('config-changed', (_, path, value) => callback(path, value));
    },
    removeConfigListener: (callback) => {
      ipcRenderer.removeListener('config-changed', callback);
    },
  },
  
  // 语音服务
  voice: {
    startListening: () => ipcRenderer.invoke('voice-start-listening'),
    stopListening: () => ipcRenderer.invoke('voice-stop-listening'),
    speak: (text) => ipcRenderer.invoke('voice-speak', text),
    onStateChange: (callback) => {
      ipcRenderer.on('voice-state-changed', (_, state) => callback(state));
    },
    onRecognitionResult: (callback) => {
      ipcRenderer.on('voice-recognition-result', (_, result) => callback(result));
    },
    removeVoiceListener: (callback) => {
      ipcRenderer.removeListener('voice-state-changed', callback);
      ipcRenderer.removeListener('voice-recognition-result', callback);
    },
  },
  
  // AI服务
  ai: {
    chat: (messages) => ipcRenderer.invoke('ai-chat', messages),
    analyzeContent: (request) => ipcRenderer.invoke('ai-analyze-content', request),
    onSuggestion: (callback) => {
      ipcRenderer.on('ai-suggestion', (_, suggestion) => callback(suggestion));
    },
    removeSuggestionListener: (callback) => {
      ipcRenderer.removeListener('ai-suggestion', callback);
    },
  },
  
  // 医疗集成
  medical: {
    search: (filters) => ipcRenderer.invoke('medical-search', filters),
    getPatient: (id) => ipcRenderer.invoke('medical-get-patient', id),
    getStudy: (id) => ipcRenderer.invoke('medical-get-study', id),
    onDataUpdate: (callback) => {
      ipcRenderer.on('medical-data-updated', (_, data) => callback(data));
    },
    removeMedicalListener: (callback) => {
      ipcRenderer.removeListener('medical-data-updated', callback);
    },
  },
  
  // 截屏功能
  screen: {
    capture: () => ipcRenderer.invoke('capture-screen'),
    onScreenCaptured: (callback) => {
      ipcRenderer.on('screen-captured', (_, data) => callback(data));
    },
    removeScreenListener: (callback) => {
      ipcRenderer.removeListener('screen-captured', callback);
    },
  },
  
  // 窗口显示控制
  windows: {
    showFloating: () => ipcRenderer.invoke('show-floating-window'),
    hideFloating: () => ipcRenderer.invoke('hide-floating-window'),
    showVoiceInput: () => ipcRenderer.invoke('show-voice-input'),
    hideVoiceInput: () => ipcRenderer.invoke('hide-voice-input'),
  },
  
  // 通知系统
  notifications: {
    show: (notification) => ipcRenderer.invoke('show-notification', notification),
    onNotification: (callback) => {
      ipcRenderer.on('notification', (_, notification) => callback(notification));
    },
    removeNotificationListener: (callback) => {
      ipcRenderer.removeListener('notification', callback);
    },
  },
  
  // 事件系统
  events: {
    on: (event, callback) => {
      ipcRenderer.on(event, (_, ...args) => callback(...args));
    },
    off: (event, callback) => {
      ipcRenderer.removeListener(event, callback);
    },
    emit: (event, ...args) => {
      ipcRenderer.send(event, ...args);
    },
  },
  
  // 系统信息
  system: {
    getInfo: () => ipcRenderer.invoke('get-system-info'),
    getPerformance: () => ipcRenderer.invoke('get-performance-info'),
  },
};

// 在开发模式下添加开发工具
if (process.env.NODE_ENV === 'development') {
  electronAPI.dev = {
    openDevTools: () => ipcRenderer.invoke('dev-open-devtools'),
    reload: () => ipcRenderer.invoke('dev-reload'),
    toggleDevTools: () => ipcRenderer.invoke('dev-toggle-devtools'),
  };
}

// 暴露API到渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明，供TypeScript使用
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// 安全检查
if (process.contextIsolated) {
  console.log('Context isolation is enabled');
} else {
  console.warn('Context isolation is disabled - this is a security risk');
}

// 预加载脚本加载完成日志
console.log('Preload script loaded successfully');

// 导出类型供其他文件使用
export type { ElectronAPI };