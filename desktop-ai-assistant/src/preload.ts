import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron';
import type { 
  WindowMode, 
  AppConfig, 
  VoiceConfig, 
  AIConfig, 
  MedicalConfig,
  ScreenCaptureOptions,
  NotificationOptions,
  AppStatus
} from './shared/types';

/**
 * Electron API 接口定义
 */
interface ElectronAPI {
  // 窗口控制
  window: {
    show: (mode: WindowMode) => Promise<void>;
    hide: (mode: WindowMode) => Promise<void>;
    toggle: (mode: WindowMode) => Promise<void>;
    close: (mode: WindowMode) => Promise<void>;
  };
  
  // 应用状态
  app: {
    getStatus: () => Promise<AppStatus>;
    quit: () => Promise<void>;
  };
  
  // 配置管理
  config: {
    get: () => Promise<AppConfig>;
    set: (config: Partial<AppConfig>) => Promise<void>;
    reset: () => Promise<void>;
  };
  
  // 语音服务
  voice: {
    startListening: () => Promise<void>;
    stopListening: () => Promise<void>;
    getState: () => Promise<any>;
    updateConfig: (config: Partial<VoiceConfig>) => Promise<void>;
    onStateChanged: (callback: (state: any) => void) => void;
    removeStateChangedListener: (callback: (state: any) => void) => void;
  };
  
  // AI服务
  ai: {
    sendMessage: (message: string, context?: any) => Promise<any>;
    getSuggestions: (context?: any) => Promise<any[]>;
    updateConfig: (config: Partial<AIConfig>) => Promise<void>;
    onStateChanged: (callback: (state: any) => void) => void;
    removeStateChangedListener: (callback: (state: any) => void) => void;
  };
  
  // 医疗集成
  medical: {
    searchPatients: (query: string) => Promise<any[]>;
    getPatient: (patientId: string) => Promise<any>;
    updateConfig: (config: Partial<MedicalConfig>) => Promise<void>;
    onStateChanged: (callback: (state: any) => void) => void;
    removeStateChangedListener: (callback: (state: any) => void) => void;
  };
  
  // 屏幕截图
  screen: {
    capture: (options?: ScreenCaptureOptions) => Promise<string | null>;
  };
  
  // 显示控制
  display: {
    showQuickSearch: (callback: () => void) => void;
    showSettings: (callback: () => void) => void;
    showAbout: (callback: () => void) => void;
    removeShowQuickSearchListener: (callback: () => void) => void;
    removeShowSettingsListener: (callback: () => void) => void;
    removeShowAboutListener: (callback: () => void) => void;
  };
  
  // 通知系统
  notification: {
    show: (options: NotificationOptions) => Promise<void>;
  };
  
  // 事件系统
  events: {
    on: (channel: string, callback: (...args: any[]) => void) => void;
    off: (channel: string, callback: (...args: any[]) => void) => void;
    once: (channel: string, callback: (...args: any[]) => void) => void;
  };
  
  // 系统信息
  system: {
    platform: string;
    version: string;
    isDevelopment: boolean;
  };
}

/**
 * 安全检查
 */
const isValidChannel = (channel: string): boolean => {
  const allowedChannels = [
    'voice-state-changed',
    'ai-state-changed', 
    'medical-state-changed',
    'show-quick-search',
    'show-settings',
    'show-about',
    'app-notification',
    'window-focus',
    'window-blur'
  ];
  return allowedChannels.includes(channel);
};

/**
 * 日志记录
 */
const log = {
  info: (message: string, ...args: any[]) => {
    console.log(`[Preload] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[Preload] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[Preload] ${message}`, ...args);
  }
};

/**
 * 创建安全的API包装器
 */
const createSafeAPI = (): ElectronAPI => {
  // 存储事件监听器
  const eventListeners = new Map<string, Set<Function>>();
  
  const addListener = (channel: string, callback: Function) => {
    if (!isValidChannel(channel)) {
      log.warn(`Invalid channel: ${channel}`);
      return;
    }
    
    if (!eventListeners.has(channel)) {
      eventListeners.set(channel, new Set());
    }
    
    eventListeners.get(channel)!.add(callback);
    ipcRenderer.on(channel, callback as any);
  };
  
  const removeListener = (channel: string, callback: Function) => {
    if (!isValidChannel(channel)) {
      log.warn(`Invalid channel: ${channel}`);
      return;
    }
    
    const listeners = eventListeners.get(channel);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        eventListeners.delete(channel);
      }
    }
    
    ipcRenderer.removeListener(channel, callback as any);
  };
  
  return {
    // 窗口控制
    window: {
      show: (mode: WindowMode) => {
        log.info(`Showing window: ${mode}`);
        return ipcRenderer.invoke('window-show', mode);
      },
      hide: (mode: WindowMode) => {
        log.info(`Hiding window: ${mode}`);
        return ipcRenderer.invoke('window-hide', mode);
      },
      toggle: (mode: WindowMode) => {
        log.info(`Toggling window: ${mode}`);
        return ipcRenderer.invoke('window-toggle', mode);
      },
      close: (mode: WindowMode) => {
        log.info(`Closing window: ${mode}`);
        return ipcRenderer.invoke('window-close', mode);
      }
    },
    
    // 应用状态
    app: {
      getStatus: () => {
        return ipcRenderer.invoke('app-get-status');
      },
      quit: () => {
        log.info('Quitting application');
        return ipcRenderer.invoke('app-quit');
      }
    },
    
    // 配置管理
    config: {
      get: () => {
        return ipcRenderer.invoke('config-get');
      },
      set: (config: Partial<AppConfig>) => {
        log.info('Updating config:', config);
        return ipcRenderer.invoke('config-set', config);
      },
      reset: () => {
        log.info('Resetting config to defaults');
        return ipcRenderer.invoke('config-reset');
      }
    },
    
    // 语音服务
    voice: {
      startListening: () => {
        log.info('Starting voice listening');
        return ipcRenderer.invoke('voice-start-listening');
      },
      stopListening: () => {
        log.info('Stopping voice listening');
        return ipcRenderer.invoke('voice-stop-listening');
      },
      getState: () => {
        return ipcRenderer.invoke('voice-get-state');
      },
      updateConfig: (config: Partial<VoiceConfig>) => {
        log.info('Updating voice config:', config);
        return ipcRenderer.invoke('voice-update-config', config);
      },
      onStateChanged: (callback: (state: any) => void) => {
        addListener('voice-state-changed', callback);
      },
      removeStateChangedListener: (callback: (state: any) => void) => {
        removeListener('voice-state-changed', callback);
      }
    },
    
    // AI服务
    ai: {
      sendMessage: (message: string, context?: any) => {
        log.info('Sending AI message:', message);
        return ipcRenderer.invoke('ai-send-message', message, context);
      },
      getSuggestions: (context?: any) => {
        return ipcRenderer.invoke('ai-get-suggestions', context);
      },
      updateConfig: (config: Partial<AIConfig>) => {
        log.info('Updating AI config:', config);
        return ipcRenderer.invoke('ai-update-config', config);
      },
      onStateChanged: (callback: (state: any) => void) => {
        addListener('ai-state-changed', callback);
      },
      removeStateChangedListener: (callback: (state: any) => void) => {
        removeListener('ai-state-changed', callback);
      }
    },
    
    // 医疗集成
    medical: {
      searchPatients: (query: string) => {
        log.info('Searching patients:', query);
        return ipcRenderer.invoke('medical-search-patients', query);
      },
      getPatient: (patientId: string) => {
        log.info('Getting patient:', patientId);
        return ipcRenderer.invoke('medical-get-patient', patientId);
      },
      updateConfig: (config: Partial<MedicalConfig>) => {
        log.info('Updating medical config:', config);
        return ipcRenderer.invoke('medical-update-config', config);
      },
      onStateChanged: (callback: (state: any) => void) => {
        addListener('medical-state-changed', callback);
      },
      removeStateChangedListener: (callback: (state: any) => void) => {
        removeListener('medical-state-changed', callback);
      }
    },
    
    // 屏幕截图
    screen: {
      capture: (options?: ScreenCaptureOptions) => {
        log.info('Capturing screen:', options);
        return ipcRenderer.invoke('screen-capture', options);
      }
    },
    
    // 显示控制
    display: {
      showQuickSearch: (callback: () => void) => {
        addListener('show-quick-search', callback);
      },
      showSettings: (callback: () => void) => {
        addListener('show-settings', callback);
      },
      showAbout: (callback: () => void) => {
        addListener('show-about', callback);
      },
      removeShowQuickSearchListener: (callback: () => void) => {
        removeListener('show-quick-search', callback);
      },
      removeShowSettingsListener: (callback: () => void) => {
        removeListener('show-settings', callback);
      },
      removeShowAboutListener: (callback: () => void) => {
        removeListener('show-about', callback);
      }
    },
    
    // 通知系统
    notification: {
      show: (options: NotificationOptions) => {
        log.info('Showing notification:', options);
        return ipcRenderer.invoke('notification-show', options);
      }
    },
    
    // 事件系统
    events: {
      on: (channel: string, callback: (...args: any[]) => void) => {
        addListener(channel, callback);
      },
      off: (channel: string, callback: (...args: any[]) => void) => {
        removeListener(channel, callback);
      },
      once: (channel: string, callback: (...args: any[]) => void) => {
        if (!isValidChannel(channel)) {
          log.warn(`Invalid channel: ${channel}`);
          return;
        }
        ipcRenderer.once(channel, callback);
      }
    },
    
    // 系统信息
    system: {
      platform: process.platform,
      version: process.versions.electron,
      isDevelopment: process.env.NODE_ENV === 'development'
    }
  };
};

/**
 * 暴露安全的API到渲染进程
 */
try {
  const electronAPI = createSafeAPI();
  
  contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  
  // 开发模式下暴露额外的调试工具
  if (process.env.NODE_ENV === 'development') {
    contextBridge.exposeInMainWorld('devTools', {
      openDevTools: () => {
        ipcRenderer.invoke('dev-open-devtools');
      },
      reload: () => {
        window.location.reload();
      },
      log: log
    });
  }
  
  log.info('Preload script loaded successfully');
  
} catch (error) {
  console.error('Failed to setup preload script:', error);
}

/**
 * 类型声明
 */
declare global {
  interface Window {
    electronAPI: ElectronAPI;
    devTools?: {
      openDevTools: () => void;
      reload: () => void;
      log: typeof log;
    };
  }
}

/**
 * 导出类型（用于TypeScript支持）
 */
export type { ElectronAPI };