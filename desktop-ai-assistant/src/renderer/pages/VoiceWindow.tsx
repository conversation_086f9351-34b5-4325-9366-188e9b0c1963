import React, { useState, useEffect, useRef } from 'react';
import { Mic, MicOff, Send, X, Volume2, VolumeX, Settings } from 'lucide-react';
import { useConfigStore } from '../stores/configStore';
import { toast } from 'sonner';

interface VoiceWindowProps {
  // Props can be passed from main process
}

interface VoiceState {
  isListening: boolean;
  isProcessing: boolean;
  isSpeaking: boolean;
  transcript: string;
  confidence: number;
  volume: number;
}

const VoiceWindow: React.FC<VoiceWindowProps> = () => {
  const { config } = useConfigStore();
  const [voiceState, setVoiceState] = useState<VoiceState>({
    isListening: false,
    isProcessing: false,
    isSpeaking: false,
    transcript: '',
    confidence: 0,
    volume: 0,
  });
  const [messages, setMessages] = useState<Array<{ role: 'user' | 'assistant'; content: string; timestamp: Date }>>([]);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const audioContextRef = useRef<AudioContext>();
  const analyserRef = useRef<AnalyserNode>();

  // Initialize voice window
  useEffect(() => {
    const initializeWindow = async () => {
      try {
        // Notify main process that voice window is ready
        if (window.electronAPI?.windowReady) {
          await window.electronAPI.windowReady('voice');
        }

        // Set up voice recognition events
        if (window.electronAPI?.onVoiceEvent) {
          window.electronAPI.onVoiceEvent((event: any) => {
            handleVoiceEvent(event);
          });
        }

        // Set up keyboard shortcuts
        document.addEventListener('keydown', handleKeyDown);

        // Initialize audio visualization
        initializeAudioVisualization();

        return () => {
          document.removeEventListener('keydown', handleKeyDown);
          if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
          }
          if (audioContextRef.current) {
            audioContextRef.current.close();
          }
        };
      } catch (error) {
        console.error('Failed to initialize voice window:', error);
      }
    };

    initializeWindow();
  }, []);

  // Initialize audio visualization
  const initializeAudioVisualization = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      analyserRef.current.fftSize = 256;
      startVisualization();
    } catch (error) {
      console.error('Failed to initialize audio visualization:', error);
    }
  };

  // Start audio visualization
  const startVisualization = () => {
    const canvas = canvasRef.current;
    const analyser = analyserRef.current;
    if (!canvas || !analyser) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      analyser.getByteFrequencyData(dataArray);
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Calculate average volume
      const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
      setVoiceState(prev => ({ ...prev, volume: average / 255 }));

      // Draw waveform
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = Math.min(centerX, centerY) - 20;
      
      ctx.strokeStyle = voiceState.isListening ? '#3B82F6' : '#6B7280';
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      for (let i = 0; i < bufferLength; i++) {
        const angle = (i / bufferLength) * 2 * Math.PI;
        const amplitude = (dataArray[i] / 255) * 30;
        const x = centerX + Math.cos(angle) * (radius + amplitude);
        const y = centerY + Math.sin(angle) * (radius + amplitude);
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.closePath();
      ctx.stroke();
      
      // Draw center circle
      ctx.fillStyle = voiceState.isListening ? '#3B82F6' : '#6B7280';
      ctx.beginPath();
      ctx.arc(centerX, centerY, 10 + average / 10, 0, 2 * Math.PI);
      ctx.fill();
      
      animationRef.current = requestAnimationFrame(draw);
    };
    
    draw();
  };

  // Handle voice events from main process
  const handleVoiceEvent = (event: any) => {
    switch (event.type) {
      case 'start':
        setVoiceState(prev => ({ ...prev, isListening: true, transcript: '' }));
        break;
      case 'transcript':
        setVoiceState(prev => ({ 
          ...prev, 
          transcript: event.transcript,
          confidence: event.confidence || 0
        }));
        break;
      case 'end':
        setVoiceState(prev => ({ ...prev, isListening: false, isProcessing: true }));
        if (event.transcript) {
          processVoiceInput(event.transcript);
        }
        break;
      case 'error':
        setVoiceState(prev => ({ ...prev, isListening: false, isProcessing: false }));
        toast.error(`语音识别错误: ${event.error}`);
        break;
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      closeWindow();
    } else if (e.key === ' ' && !voiceState.isListening) {
      e.preventDefault();
      startListening();
    } else if (e.key === 'Enter' && e.ctrlKey && voiceState.transcript) {
      sendMessage();
    }
  };

  // Start voice recognition
  const startListening = async () => {
    if (!config.voice?.enabled) {
      toast.error('语音功能未启用');
      return;
    }

    try {
      if (window.electronAPI?.startVoiceRecognition) {
        await window.electronAPI.startVoiceRecognition({
          language: config.voice.language,
          sensitivity: config.voice.sensitivity,
          continuous: true,
        });
      }
    } catch (error) {
      console.error('Failed to start voice recognition:', error);
      toast.error('无法启动语音识别');
    }
  };

  // Stop voice recognition
  const stopListening = async () => {
    try {
      if (window.electronAPI?.stopVoiceRecognition) {
        await window.electronAPI.stopVoiceRecognition();
      }
    } catch (error) {
      console.error('Failed to stop voice recognition:', error);
    }
  };

  // Process voice input
  const processVoiceInput = async (transcript: string) => {
    try {
      // Add user message
      const userMessage = {
        role: 'user' as const,
        content: transcript,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, userMessage]);

      // Process with AI
      if (window.electronAPI?.processWithAI) {
        const response = await window.electronAPI.processWithAI({
          type: 'text',
          content: transcript,
          context: messages.slice(-5), // Last 5 messages for context
        });

        if (response) {
          const assistantMessage = {
            role: 'assistant' as const,
            content: response,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, assistantMessage]);

          // Speak response if audio enabled
          if (audioEnabled && config.voice?.enabled) {
            speakResponse(response);
          }
        }
      }
    } catch (error) {
      console.error('Failed to process voice input:', error);
      toast.error('处理语音输入失败');
    } finally {
      setVoiceState(prev => ({ ...prev, isProcessing: false, transcript: '' }));
    }
  };

  // Speak AI response
  const speakResponse = async (text: string) => {
    try {
      setVoiceState(prev => ({ ...prev, isSpeaking: true }));
      
      if (window.electronAPI?.speak) {
        await window.electronAPI.speak(text, {
          language: config.voice?.language || 'zh-CN',
          rate: 1.0,
          pitch: 1.0,
        });
      }
    } catch (error) {
      console.error('Failed to speak response:', error);
    } finally {
      setVoiceState(prev => ({ ...prev, isSpeaking: false }));
    }
  };

  // Send message manually
  const sendMessage = () => {
    if (voiceState.transcript.trim()) {
      processVoiceInput(voiceState.transcript);
    }
  };

  // Close voice window
  const closeWindow = async () => {
    try {
      if (voiceState.isListening) {
        await stopListening();
      }
      if (window.electronAPI?.closeWindow) {
        await window.electronAPI.closeWindow('voice');
      }
    } catch (error) {
      console.error('Failed to close voice window:', error);
    }
  };

  return (
    <div className="w-full h-full bg-background/95 backdrop-blur-sm flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Mic className="h-5 w-5 text-primary" />
          <span className="font-medium">语音助手</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setAudioEnabled(!audioEnabled)}
            className="p-2 rounded-lg hover:bg-accent transition-colors"
            title={audioEnabled ? '关闭语音播放' : '开启语音播放'}
          >
            {audioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </button>
          
          <button
            onClick={closeWindow}
            className="p-2 rounded-lg hover:bg-accent transition-colors"
            title="关闭 (Esc)"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Audio visualization */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="relative">
          <canvas
            ref={canvasRef}
            width={300}
            height={300}
            className="rounded-full"
          />
          
          {/* Status overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              {voiceState.isListening && (
                <div className="text-primary font-medium mb-2">正在监听...</div>
              )}
              {voiceState.isProcessing && (
                <div className="text-yellow-500 font-medium mb-2">处理中...</div>
              )}
              {voiceState.isSpeaking && (
                <div className="text-green-500 font-medium mb-2">正在播放...</div>
              )}
              
              {voiceState.confidence > 0 && (
                <div className="text-sm text-muted-foreground">
                  置信度: {Math.round(voiceState.confidence * 100)}%
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Transcript and controls */}
      <div className="p-4 border-t border-border space-y-4">
        {/* Current transcript */}
        {voiceState.transcript && (
          <div className="bg-card p-3 rounded-lg border border-border">
            <div className="text-sm text-muted-foreground mb-1">识别结果:</div>
            <div className="text-foreground">{voiceState.transcript}</div>
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-center space-x-4">
          <button
            onClick={voiceState.isListening ? stopListening : startListening}
            disabled={voiceState.isProcessing}
            className={`p-4 rounded-full transition-all ${
              voiceState.isListening
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-primary hover:bg-primary/90 text-primary-foreground'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
            title={voiceState.isListening ? '停止录音' : '开始录音 (空格键)'}
          >
            {voiceState.isListening ? <MicOff className="h-6 w-6" /> : <Mic className="h-6 w-6" />}
          </button>
          
          {voiceState.transcript && (
            <button
              onClick={sendMessage}
              disabled={voiceState.isProcessing}
              className="p-3 rounded-full bg-green-500 hover:bg-green-600 text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="发送消息 (Ctrl+Enter)"
            >
              <Send className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Instructions */}
        <div className="text-center text-sm text-muted-foreground">
          <p>按空格键开始录音，Esc键关闭窗口</p>
          <p>Ctrl+Enter发送当前识别结果</p>
        </div>
      </div>

      {/* Recent messages */}
      {messages.length > 0 && (
        <div className="max-h-32 overflow-y-auto p-4 border-t border-border">
          <div className="text-sm text-muted-foreground mb-2">最近对话:</div>
          <div className="space-y-2">
            {messages.slice(-3).map((message, index) => (
              <div key={index} className="text-xs">
                <span className={`font-medium ${
                  message.role === 'user' ? 'text-blue-500' : 'text-green-500'
                }`}>
                  {message.role === 'user' ? '你' : 'AI'}:
                </span>
                <span className="ml-2 text-muted-foreground">
                  {message.content.substring(0, 50)}
                  {message.content.length > 50 ? '...' : ''}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceWindow;