/* ============================================================================
   全局样式入口文件
   ============================================================================ */

/* CSS 重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

#root {
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

/* CSS 变量定义 */
:root {
  /* 颜色系统 */
  --color-primary: #646cff;
  --color-primary-hover: #535bf2;
  --color-primary-active: #4c4fd8;
  --color-secondary: #f9f9f9;
  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-glass: rgba(255, 255, 255, 0.8);
  
  /* 文字颜色 */
  --text-primary: #213547;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-quaternary: #cbd5e1;
  --text-inverse: #ffffff;
  --text-muted: #6b7280;
  
  /* 边框颜色 */
  --border-primary: #e2e8f0;
  --border-secondary: #f1f5f9;
  --border-tertiary: #f8fafc;
  --border-focus: var(--color-primary);
  
  /* 阴影 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* 圆角 */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* 间距 */
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* 字体大小 */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-base: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-2xl: 1.5rem;
  --font-3xl: 1.875rem;
  --font-4xl: 2.25rem;
  
  /* 字体粗细 */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* 行高 */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* 过渡动画 */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-colors: color, background-color, border-color, text-decoration-color, fill, stroke 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-opacity: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index 层级 */
  --z-auto: auto;
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-modal: 1000;
  --z-popover: 1010;
  --z-tooltip: 1020;
  --z-toast: 1030;
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-primary: #646cff;
    --color-primary-hover: #747bff;
    --color-primary-active: #535bf2;
    --color-secondary: #1a1a1a;
    
    --bg-primary: #242424;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #0f0f0f;
    --bg-overlay: rgba(0, 0, 0, 0.8);
    --bg-glass: rgba(36, 36, 36, 0.8);
    
    --text-primary: rgba(255, 255, 255, 0.87);
    --text-secondary: rgba(255, 255, 255, 0.6);
    --text-tertiary: rgba(255, 255, 255, 0.4);
    --text-quaternary: rgba(255, 255, 255, 0.2);
    --text-inverse: #213547;
    --text-muted: rgba(255, 255, 255, 0.5);
    
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.05);
    --border-tertiary: rgba(255, 255, 255, 0.02);
  }
  
  body {
    color: var(--text-primary);
    background-color: var(--bg-primary);
  }
}

/* ============================================================================
   错误边界样式
   ============================================================================ */

.error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--spacing-8);
  text-align: center;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.error-boundary__icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--spacing-6);
  color: var(--color-error);
}

.error-boundary__title {
  font-size: var(--font-2xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

.error-boundary__message {
  font-size: var(--font-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-6);
  max-width: 500px;
  line-height: var(--leading-relaxed);
}

.error-boundary__details {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  max-width: 600px;
  max-height: 200px;
  overflow: auto;
  font-family: 'Courier New', monospace;
  font-size: var(--font-sm);
  text-align: left;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-boundary__actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
  justify-content: center;
}

/* ============================================================================
   加载状态样式
   ============================================================================ */

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--spacing-8);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.loading__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

.loading__text {
  font-size: var(--font-base);
  color: var(--text-secondary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============================================================================
   滚动条样式
   ============================================================================ */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-sm);
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

::-webkit-scrollbar-corner {
  background: var(--bg-secondary);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-primary) var(--bg-secondary);
}

/* ============================================================================
   文本选择样式
   ============================================================================ */

::selection {
  background: var(--color-primary);
  color: var(--text-inverse);
}

::-moz-selection {
  background: var(--color-primary);
  color: var(--text-inverse);
}

/* ============================================================================
   焦点样式
   ============================================================================ */

:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* ============================================================================
   禁用状态样式
   ============================================================================ */

:disabled,
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ============================================================================
   工具类
   ============================================================================ */

/* 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 文本截断 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ============================================================================
   响应式设计
   ============================================================================ */

/* 移动设备 */
@media (max-width: 640px) {
  body {
    font-size: var(--font-sm);
  }
  
  .error-boundary,
  .loading {
    padding: var(--spacing-4);
  }
  
  .error-boundary__title {
    font-size: var(--font-xl);
  }
  
  .error-boundary__details {
    max-height: 150px;
  }
}

/* 平板设备 */
@media (min-width: 641px) and (max-width: 1024px) {
  .error-boundary__details {
    max-width: 700px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: currentColor;
    --shadow-sm: none;
    --shadow-md: none;
    --shadow-lg: none;
    --shadow-xl: none;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .loading__spinner {
    animation: none;
    border-top-color: var(--color-primary);
  }
}

/* 打印样式 */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .error-boundary,
  .loading {
    page-break-inside: avoid;
  }
}