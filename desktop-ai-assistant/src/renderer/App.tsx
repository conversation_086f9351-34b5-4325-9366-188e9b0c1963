import React, { useEffect } from 'react';
import { Toaster } from 'sonner';
import MainWindow from './components/MainWindow';
import { useConfigStore } from './stores/configStore';

function App() {
  const { config } = useConfigStore();

  // Apply dark mode
  useEffect(() => {
    if (config.general.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [config.general.darkMode]);

  // Apply language
  useEffect(() => {
    document.documentElement.lang = config.general.language;
  }, [config.general.language]);

  return (
    <div className="h-screen overflow-hidden">
      <MainWindow />
      <Toaster 
        position="top-right" 
        theme={config.general.darkMode ? 'dark' : 'light'}
        richColors
      />
    </div>
  );
}

export default App;