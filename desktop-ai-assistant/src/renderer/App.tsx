import React, { useEffect } from 'react';
import { Toaster } from 'sonner';
import MainWindow from './components/MainWindow';
import { useConfigStore } from './stores/configStore';

function App() {
  const { config } = useConfigStore();

  // Apply dark mode
  useEffect(() => {
    const isDark = config.theme === 'dark' ||
      (config.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [config.theme]);

  // Apply language
  useEffect(() => {
    document.documentElement.lang = config.language;
  }, [config.language]);

  return (
    <div className="h-screen overflow-hidden">
      <MainWindow />
      <Toaster
        position="top-right"
        theme={config.theme === 'dark' ||
          (config.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)
          ? 'dark' : 'light'}
        richColors
      />
    </div>
  );
}

export default App;