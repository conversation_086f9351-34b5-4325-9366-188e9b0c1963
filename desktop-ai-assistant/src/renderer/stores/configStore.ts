import { create } from 'zustand';
import type { AppConfig } from '../../shared/types';

interface ConfigState {
  config: AppConfig;
  updateConfig: (updates: Partial<AppConfig>) => void;
  resetConfig: () => void;
  loadConfig: () => Promise<void>;
  saveConfig: () => Promise<void>;
}

const defaultConfig: AppConfig = {
  version: "1.0.0",
  firstRun: false,
  theme: 'auto',
  language: 'zh-CN',
  windows: {
    main: {
      width: 1200,
      height: 800,
      x: 0,
      y: 0,
      alwaysOnTop: false,
      opacity: 1
    },
    floating: {
      width: 400,
      height: 300,
      alwaysOnTop: true,
      opacity: 0.9
    },
    voice: {
      width: 300,
      height: 200,
      alwaysOnTop: true,
      opacity: 0.95
    }
  },
  shortcuts: {
    enabled: true,
    toggleMainWindow: 'CommandOrControl+Shift+A',
    toggleFloatingWindow: 'CommandOrControl+Shift+F',
    voiceInput: 'CommandOrControl+Shift+V',
    screenCapture: 'CommandOrControl+Shift+S',
    quickSearch: 'CommandOrControl+Shift+Q',
    showSettings: 'CommandOrControl+Comma',
    quit: 'CommandOrControl+Q'
  },
  voice: {
    enabled: true,
    language: 'zh-CN',
    continuous: false,
    interimResults: true,
    maxAlternatives: 1,
    hotwordEnabled: true,
    hotwords: ['小助手'],
    sensitivity: 0.7,
    noiseReduction: true,
    echoCancellation: true,
    autoGainControl: true,
    sampleRate: 16000,
    channels: 1,
    voiceActivityDetection: true,
    silenceTimeout: 3000,
    speechTimeout: 10000,
    ttsEnabled: true,
    ttsVoice: 'default',
    ttsRate: 1,
    ttsPitch: 1,
    ttsVolume: 0.8
  },
  ai: {
    enabled: false,
    provider: 'openai',
    apiKey: '',
    apiUrl: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2048,
    maxHistory: 10,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: '你是一个智能桌面助手，可以帮助用户处理各种任务。',
    contextLength: 4096,
    streamResponse: true,
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
    rateLimitRpm: 60,
    rateLimitTpm: 10000
  },
  medical: {
    enabled: true,
    apiUrl: 'http://localhost:3001/api',
    apiKey: '',
    username: '',
    password: '',
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000,
    cacheEnabled: true,
    cacheTtl: 300000,
    rateLimitRpm: 60,
    endpoints: {
      patients: '/patients',
      records: '/records',
      diagnoses: '/diagnoses',
      treatments: '/treatments',
      medications: '/medications',
      appointments: '/appointments'
    },
    features: {
      patientSearch: true,
      recordAccess: true,
      diagnosisAssist: true,
      treatmentPlan: true,
      medicationCheck: true,
      appointmentSync: true
    }
  },
  desktopRecognition: {
    enabled: false,
    ocrEnabled: true,
    accessibilityEnabled: true,
    screenCaptureInterval: 5000,
    ocrLanguages: ['zh-CN', 'en-US'],
    confidenceThreshold: 0.8,
    excludeApps: [],
    includeApps: [],
    sensitiveDataFilter: true,
    dataRetention: 86400000
  },
  privacy: {
    dataCollection: false,
    analytics: false,
    crashReports: true,
    localProcessing: true,
    dataEncryption: true,
    autoCleanup: true,
    cleanupInterval: 86400000,
    sensitiveDataMask: true,
    auditLog: true,
    dataExport: false
  },
  notifications: {
    enabled: true,
    sound: true,
    badge: true,
    position: 'top-right',
    duration: 5000,
    maxCount: 5,
    priority: 'normal',
    categories: {
      system: true,
      voice: true,
      ai: true,
      medical: true,
      error: true
    }
  },
  startup: {
    autoStart: false,
    minimizeToTray: true,
    showFloatingWindow: false,
    checkUpdates: true
  },
  performance: {
    hardwareAcceleration: true,
    backgroundThrottling: true,
    memoryLimit: 512,
    cpuLimit: 50,
    diskCacheSize: 100,
    networkTimeout: 10000,
    maxConcurrentRequests: 5,
    debounceDelay: 300,
    throttleDelay: 100
  },
  logging: {
    level: 'info',
    file: true,
    console: true,
    remote: false,
    maxFileSize: 10,
    maxFiles: 5
  }
};

export const useConfigStore = create<ConfigState>((set, get) => ({
  config: defaultConfig,

  updateConfig: (updates: Partial<AppConfig>) => {
    set((state) => ({
      config: {
        ...state.config,
        ...updates,
        // Deep merge for nested objects
        windows: { ...state.config.windows, ...updates.windows },
        voice: { ...state.config.voice, ...updates.voice },
        shortcuts: { ...state.config.shortcuts, ...updates.shortcuts },
        privacy: { ...state.config.privacy, ...updates.privacy },
        medical: { ...state.config.medical, ...updates.medical },
        ai: { ...state.config.ai, ...updates.ai },
        desktopRecognition: { ...state.config.desktopRecognition, ...updates.desktopRecognition },
        notifications: { ...state.config.notifications, ...updates.notifications },
        startup: { ...state.config.startup, ...updates.startup },
        performance: { ...state.config.performance, ...updates.performance },
        logging: { ...state.config.logging, ...updates.logging },
      },
    }));

    // Auto-save to main process
    get().saveConfig();
  },

  resetConfig: () => {
    set({ config: defaultConfig });
    get().saveConfig();
  },

  loadConfig: async () => {
    try {
      // In development, we'll use the default config
      // In production, this would load from the main process
      console.log('Loading config from main process...');
    } catch (error) {
      console.error('Failed to load config:', error);
    }
  },

  saveConfig: async () => {
    try {
      // In development, we'll just log the config
      // In production, this would save to the main process
      console.log('Saving config to main process:', get().config);
    } catch (error) {
      console.error('Failed to save config:', error);
    }
  },
}));

// Load full config on app start
if (typeof window !== 'undefined') {
  useConfigStore.getState().loadConfig();
}