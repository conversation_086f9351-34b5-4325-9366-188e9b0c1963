import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { AppConfig } from '../../shared/types';

interface ConfigState {
  config: AppConfig;
  updateConfig: (updates: Partial<AppConfig>) => void;
  resetConfig: () => void;
  loadConfig: () => Promise<void>;
  saveConfig: () => Promise<void>;
}

const defaultConfig: AppConfig = {
  general: {
    startOnBoot: false,
    minimizeToTray: true,
    showNotifications: true,
    language: 'zh-CN',
    autoUpdate: true,
  },
  voice: {
    enabled: true,
    language: 'zh-CN',
    hotkey: 'CommandOrControl+Shift+V',
    sensitivity: 0.7,
    autoStop: true,
    autoStopDelay: 3000,
    wakeWord: '小助手',
    enableWakeWord: false,
  },
  shortcuts: {
    toggleMainWindow: 'CommandOrControl+Shift+A',
    toggleFloatingWindow: 'CommandOrControl+Shift+F',
    toggleVoiceWindow: 'CommandOrControl+Shift+V',
    openSettings: 'CommandOrControl+Comma',
    quickCapture: 'CommandOrControl+Shift+C',
    emergencyHide: 'Escape',
  },
  ui: {
    theme: 'system',
    fontSize: 'medium',
    density: 'comfortable',
    animations: true,
    transparency: 0.95,
    alwaysOnTop: false,
    showInTaskbar: true,
    toastDuration: 4000,
  },
  privacy: {
    dataCollection: 'minimal',
    shareUsageStats: false,
    localProcessing: true,
    encryptStorage: true,
    autoDeleteHistory: false,
    historyRetentionDays: 30,
  },
  medical: {
    enabled: true,
    apiEndpoint: 'http://localhost:3001/api',
    timeout: 10000,
    retryAttempts: 3,
    cacheEnabled: true,
    cacheDuration: 300000, // 5 minutes
  },
  ai: {
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,
    contextWindow: 4096,
    streamResponse: true,
  },
  desktop: {
    ocrEnabled: true,
    accessibilityEnabled: true,
    screenshotQuality: 0.8,
    captureDelay: 500,
    excludeApps: ['1Password', 'Keychain Access'],
    monitorChanges: true,
  },
};

export const useConfigStore = create<ConfigState>(
  persist(
    (set, get) => ({
      config: defaultConfig,
      
      updateConfig: (updates: Partial<AppConfig>) => {
        set((state) => ({
          config: {
            ...state.config,
            ...updates,
            // Deep merge for nested objects
            general: { ...state.config.general, ...updates.general },
            voice: { ...state.config.voice, ...updates.voice },
            shortcuts: { ...state.config.shortcuts, ...updates.shortcuts },
            ui: { ...state.config.ui, ...updates.ui },
            privacy: { ...state.config.privacy, ...updates.privacy },
            medical: { ...state.config.medical, ...updates.medical },
            ai: { ...state.config.ai, ...updates.ai },
            desktop: { ...state.config.desktop, ...updates.desktop },
          },
        }));
        
        // Auto-save to main process
        get().saveConfig();
      },
      
      resetConfig: () => {
        set({ config: defaultConfig });
        get().saveConfig();
      },
      
      loadConfig: async () => {
        try {
          if (window.electronAPI?.getConfig) {
            const savedConfig = await window.electronAPI.getConfig();
            if (savedConfig) {
              set({ config: { ...defaultConfig, ...savedConfig } });
            }
          }
        } catch (error) {
          console.error('Failed to load config:', error);
        }
      },
      
      saveConfig: async () => {
        try {
          if (window.electronAPI?.saveConfig) {
            await window.electronAPI.saveConfig(get().config);
          }
        } catch (error) {
          console.error('Failed to save config:', error);
        }
      },
    }),
    {
      name: 'app-config',
      version: 1,
      // Only persist essential UI settings, load full config from main process
      partialize: (state) => ({
        config: {
          ui: state.config.ui,
          general: {
            language: state.config.general.language,
          },
        },
      }),
    }
  )
);

// Load full config on app start
if (typeof window !== 'undefined') {
  useConfigStore.getState().loadConfig();
}