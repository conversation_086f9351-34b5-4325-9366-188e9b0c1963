import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  getSystemTheme: () => 'light' | 'dark';
  getEffectiveTheme: () => 'light' | 'dark';
}

export const useThemeStore = create<ThemeState>(
  persist(
    (set, get) => ({
      theme: 'system',
      
      setTheme: (theme: Theme) => {
        set({ theme });
        
        // Notify main process about theme change
        if (window.electronAPI?.setTheme) {
          window.electronAPI.setTheme(theme);
        }
      },
      
      toggleTheme: () => {
        const { theme, getEffectiveTheme } = get();
        
        if (theme === 'system') {
          // If system theme, switch to opposite of current effective theme
          const effectiveTheme = getEffectiveTheme();
          set({ theme: effectiveTheme === 'dark' ? 'light' : 'dark' });
        } else {
          // Toggle between light and dark
          set({ theme: theme === 'dark' ? 'light' : 'dark' });
        }
        
        // Notify main process
        if (window.electronAPI?.setTheme) {
          window.electronAPI.setTheme(get().theme);
        }
      },
      
      getSystemTheme: () => {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      },
      
      getEffectiveTheme: () => {
        const { theme, getSystemTheme } = get();
        return theme === 'system' ? getSystemTheme() : theme;
      },
    }),
    {
      name: 'theme-storage',
      version: 1,
    }
  )
);

// Listen for system theme changes
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  mediaQuery.addEventListener('change', () => {
    const { theme, setTheme } = useThemeStore.getState();
    if (theme === 'system') {
      // Force re-render by setting theme again
      setTheme('system');
    }
  });
}