import React, { useState, useEffect } from 'react';
import { Settings, MessageSquare, Monitor, Activity, X, Minus, Square } from 'lucide-react';
import Chat from './Chat';
import DesktopRecognition from './DesktopRecognition';
import MedicalSystem from './MedicalSystem';
import SettingsPanel from './SettingsPanel';
import { useConfigStore } from '../stores/configStore';

type TabType = 'chat' | 'desktop' | 'medical' | 'settings';

interface MainWindowProps {
  onClose?: () => void;
  onMinimize?: () => void;
  onMaximize?: () => void;
}

const MainWindow: React.FC<MainWindowProps> = ({ onClose, onMinimize, onMaximize }) => {
  const [activeTab, setActiveTab] = useState<TabType>('chat');
  const [isMaximized, setIsMaximized] = useState(false);
  const { config } = useConfigStore();

  // Handle window controls
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else if (window.electronAPI?.closeWindow) {
      window.electronAPI.closeWindow();
    }
  };

  const handleMinimize = () => {
    if (onMinimize) {
      onMinimize();
    } else if (window.electronAPI?.minimizeWindow) {
      window.electronAPI.minimizeWindow();
    }
  };

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
    if (onMaximize) {
      onMaximize();
    } else if (window.electronAPI?.maximizeWindow) {
      window.electronAPI.maximizeWindow();
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + W to close
      if ((event.ctrlKey || event.metaKey) && event.key === 'w') {
        event.preventDefault();
        handleClose();
      }
      
      // Ctrl/Cmd + M to minimize
      if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
        event.preventDefault();
        handleMinimize();
      }
      
      // Tab switching shortcuts
      if ((event.ctrlKey || event.metaKey) && event.key >= '1' && event.key <= '4') {
        event.preventDefault();
        const tabIndex = parseInt(event.key) - 1;
        const tabs: TabType[] = ['chat', 'desktop', 'medical', 'settings'];
        setActiveTab(tabs[tabIndex]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const tabs = [
    {
      id: 'chat' as TabType,
      name: 'AI对话',
      icon: MessageSquare,
      enabled: true
    },
    {
      id: 'desktop' as TabType,
      name: '桌面识别',
      icon: Monitor,
      enabled: config.desktop.enabled
    },
    {
      id: 'medical' as TabType,
      name: '医疗系统',
      icon: Activity,
      enabled: config.medical.enabled
    },
    {
      id: 'settings' as TabType,
      name: '设置',
      icon: Settings,
      enabled: true
    }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'chat':
        return <Chat />;
      case 'desktop':
        return <DesktopRecognition />;
      case 'medical':
        return <MedicalSystem />;
      case 'settings':
        return <SettingsPanel />;
      default:
        return <Chat />;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      {/* Custom title bar */}
      <div className="flex items-center justify-between bg-white/70 dark:bg-gray-800/70 backdrop-blur-xs border-b border-gray-200 dark:border-gray-700 px-4 py-2 select-none">
        {/* App title */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">AI助手对话</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">不打扰、隐身、谁叫谁出</span>
        </div>

        {/* Quick settings */}
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setActiveTab('settings')}
            className="inline-flex items-center space-x-1 px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            title="打开设置"
          >
            <Settings className="w-4 h-4" />
            <span className="text-xs">设置</span>
          </button>
        </div>
      </div>

      {/* Tab navigation */}
      <div className="flex bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;
          const isDisabled = !tab.enabled;
          
          return (
            <button
              key={tab.id}
              onClick={() => tab.enabled && setActiveTab(tab.id)}
              disabled={isDisabled}
              className={`
                flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-colors
                ${isActive
                  ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : isDisabled
                    ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                }
              `}
              title={isDisabled ? `${tab.name} (未启用)` : `${tab.name} (Cmd+${tabs.indexOf(tab) + 1})`}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.name}</span>
              {isDisabled && (
                <span className="text-xs text-gray-400 dark:text-gray-600">(未启用)</span>
              )}
            </button>
          );
        })}
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-hidden">
        {renderContent()}
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-800/60 border-t border-gray-200 dark:border-gray-700 px-4 py-1 text-xs text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          <span>状态: 就绪</span>
          {config.voice.enabled && (
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>语音: 已启用</span>
            </span>
          )}
          {config.desktop.enabled && (
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span>桌面识别: 已启用</span>
            </span>
          )}
          {config.medical.enabled && (
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span>医疗系统: 已连接</span>
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <span>版本 1.0.0</span>
        </div>
      </div>
    </div>
  );
};

export default MainWindow;