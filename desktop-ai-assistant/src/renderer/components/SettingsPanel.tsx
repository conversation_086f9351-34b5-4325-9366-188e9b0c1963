import React, { useState } from 'react';
import { Save, RefreshCw, Download, Upload, AlertCircle, CheckCircle, Settings, Volume2, Monitor, Activity } from 'lucide-react';
import { useConfigStore } from '../stores/configStore';
import { toast } from 'sonner';

type SettingsSection = 'general' | 'voice' | 'desktop' | 'medical' | 'advanced';

const SettingsPanel: React.FC = () => {
  const { config, updateConfig, resetConfig } = useConfigStore();
  const [activeSection, setActiveSection] = useState<SettingsSection>('general');
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Handle config changes
  const handleConfigChange = (path: string, value: any) => {
    const keys = path.split('.');
    const newConfig = { ...config };
    let current = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    updateConfig(newConfig);
    setHasUnsavedChanges(true);
  };

  // Save configuration
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Simulate API call to save config
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (window.electronAPI?.saveConfig) {
        await window.electronAPI.saveConfig(config);
      }
      
      setHasUnsavedChanges(false);
      toast.success('设置已保存');
    } catch (error) {
      toast.error('保存设置失败');
    } finally {
      setIsSaving(false);
    }
  };

  // Reset to defaults
  const handleReset = () => {
    if (confirm('确定要重置所有设置到默认值吗？')) {
      resetConfig();
      setHasUnsavedChanges(true);
      toast.success('设置已重置');
    }
  };

  // Export configuration
  const handleExport = () => {
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'desktop-ai-assistant-config.json';
    link.click();
    URL.revokeObjectURL(url);
    toast.success('配置已导出');
  };

  // Import configuration
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedConfig = JSON.parse(e.target?.result as string);
        updateConfig(importedConfig);
        setHasUnsavedChanges(true);
        toast.success('配置已导入');
      } catch (error) {
        toast.error('导入配置失败：文件格式错误');
      }
    };
    reader.readAsText(file);
  };

  const sections = [
    { id: 'general' as SettingsSection, name: '常规设置', icon: Settings },
    { id: 'voice' as SettingsSection, name: '语音设置', icon: Volume2 },
    { id: 'desktop' as SettingsSection, name: '桌面识别', icon: Monitor },
    { id: 'medical' as SettingsSection, name: '医疗系统', icon: Activity },
    { id: 'advanced' as SettingsSection, name: '高级设置', icon: AlertCircle }
  ];

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">应用设置</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">开机自启动</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">应用程序随系统启动</p>
            </div>
            <input
              type="checkbox"
              checked={config.general.autoStart}
              onChange={(e) => handleConfigChange('general.autoStart', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">最小化到系统托盘</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">关闭窗口时最小化到托盘而不是退出</p>
            </div>
            <input
              type="checkbox"
              checked={config.general.minimizeToTray}
              onChange={(e) => handleConfigChange('general.minimizeToTray', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">深色模式</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">使用深色主题</p>
            </div>
            <input
              type="checkbox"
              checked={config.general.darkMode}
              onChange={(e) => handleConfigChange('general.darkMode', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">语言</label>
            <select
              value={config.general.language}
              onChange={(e) => handleConfigChange('general.language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            >
              <option value="zh-CN">简体中文</option>
              <option value="en-US">English</option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">快捷键设置</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">全局唤醒快捷键</label>
            <input
              type="text"
              value={config.general.globalHotkey}
              onChange={(e) => handleConfigChange('general.globalHotkey', e.target.value)}
              placeholder="例如: Ctrl+Shift+A"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">设置全局快捷键来快速唤醒AI助手</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderVoiceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">语音功能</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">启用语音识别</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">允许通过语音与AI助手交互</p>
            </div>
            <input
              type="checkbox"
              checked={config.voice.enabled}
              onChange={(e) => handleConfigChange('voice.enabled', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">语音唤醒词</label>
            <input
              type="text"
              value={config.voice.wakeWord}
              onChange={(e) => handleConfigChange('voice.wakeWord', e.target.value)}
              placeholder="例如: 小助手"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">语音识别语言</label>
            <select
              value={config.voice.language}
              onChange={(e) => handleConfigChange('voice.language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            >
              <option value="zh-CN">中文（简体）</option>
              <option value="en-US">English (US)</option>
              <option value="ja-JP">日本語</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">语音合成语言</label>
            <select
              value={config.voice.ttsLanguage}
              onChange={(e) => handleConfigChange('voice.ttsLanguage', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            >
              <option value="zh-CN">中文（简体）</option>
              <option value="en-US">English (US)</option>
              <option value="ja-JP">日本語</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDesktopSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">桌面识别功能</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">启用桌面识别</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">允许AI助手识别和分析桌面内容</p>
            </div>
            <input
              type="checkbox"
              checked={config.desktop.enabled}
              onChange={(e) => handleConfigChange('desktop.enabled', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">自动分析截图</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">截图后自动进行AI分析</p>
            </div>
            <input
              type="checkbox"
              checked={config.desktop.autoAnalyze}
              onChange={(e) => handleConfigChange('desktop.autoAnalyze', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">监控间隔（秒）</label>
            <input
              type="number"
              min="5"
              max="300"
              value={config.desktop.monitoringInterval}
              onChange={(e) => handleConfigChange('desktop.monitoringInterval', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">自动截图的时间间隔</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">截图质量</label>
            <select
              value={config.desktop.screenshotQuality}
              onChange={(e) => handleConfigChange('desktop.screenshotQuality', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            >
              <option value="low">低质量（快速）</option>
              <option value="medium">中等质量</option>
              <option value="high">高质量（精确）</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMedicalSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">医疗系统集成</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">启用医疗系统</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">连接到医疗管理系统</p>
            </div>
            <input
              type="checkbox"
              checked={config.medical.enabled}
              onChange={(e) => handleConfigChange('medical.enabled', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API端点</label>
            <input
              type="url"
              value={config.medical.apiEndpoint}
              onChange={(e) => handleConfigChange('medical.apiEndpoint', e.target.value)}
              placeholder="https://api.medical-system.com"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API密钥</label>
            <input
              type="password"
              value={config.medical.apiKey}
              onChange={(e) => handleConfigChange('medical.apiKey', e.target.value)}
              placeholder="输入API密钥"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">数据同步间隔（分钟）</label>
            <input
              type="number"
              min="1"
              max="60"
              value={config.medical.syncInterval}
              onChange={(e) => handleConfigChange('medical.syncInterval', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderAdvancedSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">高级设置</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">调试模式</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">启用详细日志记录</p>
            </div>
            <input
              type="checkbox"
              checked={config.advanced.debugMode}
              onChange={(e) => handleConfigChange('advanced.debugMode', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">日志级别</label>
            <select
              value={config.advanced.logLevel}
              onChange={(e) => handleConfigChange('advanced.logLevel', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            >
              <option value="error">错误</option>
              <option value="warn">警告</option>
              <option value="info">信息</option>
              <option value="debug">调试</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大日志文件大小（MB）</label>
            <input
              type="number"
              min="1"
              max="100"
              value={config.advanced.maxLogSize}
              onChange={(e) => handleConfigChange('advanced.maxLogSize', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-300"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">自动更新</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">自动检查并安装更新</p>
            </div>
            <input
              type="checkbox"
              checked={config.advanced.autoUpdate}
              onChange={(e) => handleConfigChange('advanced.autoUpdate', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">配置管理</h3>
        
        <div className="flex space-x-4">
          <button
            onClick={handleExport}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>导出配置</span>
          </button>
          
          <label className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors cursor-pointer">
            <Upload className="w-4 h-4" />
            <span>导入配置</span>
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              className="hidden"
            />
          </label>
          
          <button
            onClick={handleReset}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>重置设置</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'voice':
        return renderVoiceSettings();
      case 'desktop':
        return renderDesktopSettings();
      case 'medical':
        return renderMedicalSettings();
      case 'advanced':
        return renderAdvancedSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="flex h-full bg-white dark:bg-gray-900">
      {/* Sidebar */}
      <div className="w-64 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <div className="p-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">设置</h2>
          <nav className="space-y-1">
            {sections.map((section) => {
              const Icon = section.icon;
              const isActive = activeSection === section.id;
              
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`
                    w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${isActive
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  <span>{section.name}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {sections.find(s => s.id === activeSection)?.name}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                配置您的桌面AI助手设置
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              {hasUnsavedChanges && (
                <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">有未保存的更改</span>
                </div>
              )}
              
              <button
                onClick={handleSave}
                disabled={isSaving || !hasUnsavedChanges}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSaving ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSaving ? '保存中...' : '保存设置'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;