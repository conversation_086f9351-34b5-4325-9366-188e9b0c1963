import React, { useState, useRef, useEffect } from 'react';
import { Camera, Eye, EyeOff, Monitor, Scan, RefreshCw, Download, Settings, Play, Pause } from 'lucide-react';
import { useConfigStore } from '../stores/configStore';
import { toast } from 'sonner';

interface ScreenshotData {
  id: string;
  timestamp: Date;
  imageUrl: string;
  analysis?: {
    text: string;
    elements: {
      type: string;
      content: string;
      position: { x: number; y: number; width: number; height: number };
      confidence: number;
    }[];
    suggestions: string[];
  };
}

interface DesktopRecognitionProps {
  className?: string;
}

const DesktopRecognition: React.FC<DesktopRecognitionProps> = ({ className = '' }) => {
  const { config } = useConfigStore();
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [screenshots, setScreenshots] = useState<ScreenshotData[]>([]);
  const [selectedScreenshot, setSelectedScreenshot] = useState<ScreenshotData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [monitoringInterval, setMonitoringInterval] = useState<NodeJS.Timeout | null>(null);
  const [showOverlay, setShowOverlay] = useState(false);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const previewRef = useRef<HTMLImageElement>(null);

  // Initialize desktop recognition
  useEffect(() => {
    return () => {
      // Cleanup monitoring on unmount
      if (monitoringInterval) {
        clearInterval(monitoringInterval);
      }
    };
  }, [monitoringInterval]);

  // Capture desktop screenshot
  const captureDesktop = async () => {
    if (!config.desktop?.enabled) {
      toast.error('桌面识别功能未启用，请在设置中开启');
      return;
    }

    setIsCapturing(true);
    try {
      const imageData = await window.electronAPI?.captureDesktop?.();
      if (imageData) {
        const screenshot: ScreenshotData = {
          id: Date.now().toString(),
          timestamp: new Date(),
          imageUrl: imageData,
        };
        
        setScreenshots(prev => [screenshot, ...prev.slice(0, 9)]); // Keep last 10 screenshots
        setSelectedScreenshot(screenshot);
        toast.success('桌面截图已捕获');
        
        // Auto-analyze if enabled
        if (config.desktop?.autoAnalyze) {
          analyzeScreenshot(screenshot);
        }
      }
    } catch (error) {
      console.error('Failed to capture desktop:', error);
      toast.error('桌面截图失败');
    } finally {
      setIsCapturing(false);
    }
  };

  // Analyze screenshot with AI
  const analyzeScreenshot = async (screenshot: ScreenshotData) => {
    setIsAnalyzing(true);
    try {
      const analysis = await window.electronAPI?.processWithAI?.({
        message: '请分析这个桌面截图，识别其中的文本、UI元素和可能的操作建议',
        attachments: [{
          type: 'image',
          name: 'desktop-screenshot.png',
          url: screenshot.imageUrl,
        }],
        context: 'desktop-analysis',
      });

      if (analysis) {
        const updatedScreenshot = {
          ...screenshot,
          analysis: {
            text: analysis,
            elements: [], // Would be populated by more sophisticated analysis
            suggestions: [
              '检测到文本内容，可以进行OCR识别',
              '发现可点击元素，可以执行自动化操作',
              '识别到表单字段，可以协助填写',
            ],
          },
        };
        
        setScreenshots(prev => 
          prev.map(s => s.id === screenshot.id ? updatedScreenshot : s)
        );
        setSelectedScreenshot(updatedScreenshot);
        toast.success('桌面分析完成');
      }
    } catch (error) {
      console.error('Failed to analyze screenshot:', error);
      toast.error('桌面分析失败');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Start/stop monitoring
  const toggleMonitoring = () => {
    if (isMonitoring) {
      // Stop monitoring
      if (monitoringInterval) {
        clearInterval(monitoringInterval);
        setMonitoringInterval(null);
      }
      setIsMonitoring(false);
      toast.success('桌面监控已停止');
    } else {
      // Start monitoring
      const interval = setInterval(() => {
        captureDesktop();
      }, (config.desktop?.monitoringInterval || 30) * 1000);
      
      setMonitoringInterval(interval);
      setIsMonitoring(true);
      toast.success('桌面监控已启动');
    }
  };

  // Download screenshot
  const downloadScreenshot = (screenshot: ScreenshotData) => {
    const link = document.createElement('a');
    link.href = screenshot.imageUrl;
    link.download = `desktop-screenshot-${screenshot.timestamp.toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('截图已下载');
  };

  // Clear all screenshots
  const clearScreenshots = () => {
    if (confirm('确定要清空所有截图吗？')) {
      setScreenshots([]);
      setSelectedScreenshot(null);
      toast.success('截图已清空');
    }
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Toggle overlay display
  const toggleOverlay = () => {
    setShowOverlay(!showOverlay);
  };

  // Draw analysis overlay on canvas
  const drawAnalysisOverlay = () => {
    if (!selectedScreenshot?.analysis || !canvasRef.current || !previewRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const img = previewRef.current;
    
    if (!ctx) return;

    // Set canvas size to match image
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (showOverlay) {
      // Draw detected elements (mock data for demonstration)
      const mockElements = [
        { x: 100, y: 100, width: 200, height: 30, type: 'text', confidence: 0.95 },
        { x: 150, y: 200, width: 100, height: 40, type: 'button', confidence: 0.88 },
        { x: 300, y: 150, width: 250, height: 20, type: 'text', confidence: 0.92 },
      ];
      
      mockElements.forEach(element => {
        ctx.strokeStyle = element.type === 'button' ? '#ff6b6b' : '#4ecdc4';
        ctx.lineWidth = 2;
        ctx.strokeRect(element.x, element.y, element.width, element.height);
        
        // Draw label
        ctx.fillStyle = element.type === 'button' ? '#ff6b6b' : '#4ecdc4';
        ctx.font = '12px Arial';
        ctx.fillText(
          `${element.type} (${Math.round(element.confidence * 100)}%)`,
          element.x,
          element.y - 5
        );
      });
    }
  };

  // Update overlay when screenshot or overlay state changes
  useEffect(() => {
    if (previewRef.current?.complete) {
      drawAnalysisOverlay();
    }
  }, [selectedScreenshot, showOverlay]);

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div>
          <h2 className="text-lg font-semibold">桌面识别</h2>
          <p className="text-sm text-muted-foreground">
            捕获和分析桌面内容，识别文本和UI元素
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMonitoring}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
              isMonitoring
                ? 'bg-red-500 text-white hover:bg-red-600'
                : 'bg-primary text-primary-foreground hover:bg-primary/90'
            }`}
            disabled={!config.desktop?.enabled}
          >
            {isMonitoring ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            <span>{isMonitoring ? '停止监控' : '开始监控'}</span>
          </button>
          
          <button
            onClick={captureDesktop}
            disabled={isCapturing || !config.desktop?.enabled}
            className="flex items-center space-x-2 px-3 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCapturing ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            ) : (
              <Camera className="h-4 w-4" />
            )}
            <span>{isCapturing ? '捕获中...' : '立即截图'}</span>
          </button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Screenshot List */}
        <div className="w-80 border-r border-border bg-card">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">截图历史</h3>
              <button
                onClick={clearScreenshots}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                disabled={screenshots.length === 0}
              >
                清空
              </button>
            </div>
            
            {isMonitoring && (
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>监控中 (每{config.desktop?.monitoringInterval || 30}秒)</span>
              </div>
            )}
          </div>
          
          <div className="overflow-y-auto h-full">
            {screenshots.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                <Monitor className="h-12 w-12 mb-4" />
                <p className="text-sm text-center">暂无截图</p>
                <p className="text-xs text-center mt-1">点击"立即截图"开始捕获桌面</p>
              </div>
            ) : (
              <div className="p-2 space-y-2">
                {screenshots.map((screenshot) => (
                  <div
                    key={screenshot.id}
                    onClick={() => setSelectedScreenshot(screenshot)}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedScreenshot?.id === screenshot.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:bg-accent'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <img
                        src={screenshot.imageUrl}
                        alt="Desktop screenshot"
                        className="w-16 h-12 object-cover rounded border"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {formatTime(screenshot.timestamp)}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          {screenshot.analysis ? (
                            <span className="text-xs text-green-600 flex items-center">
                              <Scan className="h-3 w-3 mr-1" />
                              已分析
                            </span>
                          ) : (
                            <span className="text-xs text-muted-foreground">未分析</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {selectedScreenshot ? (
            <>
              {/* Preview Controls */}
              <div className="flex items-center justify-between p-4 border-b border-border">
                <div className="flex items-center space-x-4">
                  <h3 className="font-medium">
                    截图预览 - {formatTime(selectedScreenshot.timestamp)}
                  </h3>
                  
                  {selectedScreenshot.analysis && (
                    <button
                      onClick={toggleOverlay}
                      className={`flex items-center space-x-2 px-3 py-1 rounded-lg text-sm transition-colors ${
                        showOverlay
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                      }`}
                    >
                      {showOverlay ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      <span>{showOverlay ? '隐藏标注' : '显示标注'}</span>
                    </button>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  {!selectedScreenshot.analysis && (
                    <button
                      onClick={() => analyzeScreenshot(selectedScreenshot)}
                      disabled={isAnalyzing}
                      className="flex items-center space-x-2 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isAnalyzing ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <Scan className="h-4 w-4" />
                      )}
                      <span>{isAnalyzing ? '分析中...' : '分析截图'}</span>
                    </button>
                  )}
                  
                  <button
                    onClick={() => downloadScreenshot(selectedScreenshot)}
                    className="p-2 rounded-lg border border-border hover:bg-accent transition-colors"
                    title="下载截图"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Preview Area */}
              <div className="flex-1 overflow-auto p-4">
                <div className="relative inline-block">
                  <img
                    ref={previewRef}
                    src={selectedScreenshot.imageUrl}
                    alt="Desktop screenshot"
                    className="max-w-full h-auto border border-border rounded-lg shadow-lg"
                    onLoad={drawAnalysisOverlay}
                  />
                  
                  {/* Analysis overlay canvas */}
                  <canvas
                    ref={canvasRef}
                    className="absolute top-0 left-0 pointer-events-none"
                    style={{
                      width: previewRef.current?.offsetWidth || 'auto',
                      height: previewRef.current?.offsetHeight || 'auto',
                    }}
                  />
                </div>
              </div>

              {/* Analysis Results */}
              {selectedScreenshot.analysis && (
                <div className="border-t border-border p-4 bg-card">
                  <h4 className="font-medium mb-3">分析结果</h4>
                  
                  <div className="space-y-4">
                    {/* AI Analysis Text */}
                    <div>
                      <h5 className="text-sm font-medium mb-2">AI分析</h5>
                      <div className="p-3 bg-background rounded-lg border text-sm">
                        {selectedScreenshot.analysis.text}
                      </div>
                    </div>
                    
                    {/* Suggestions */}
                    {selectedScreenshot.analysis.suggestions.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium mb-2">操作建议</h5>
                        <div className="space-y-2">
                          {selectedScreenshot.analysis.suggestions.map((suggestion, index) => (
                            <div
                              key={index}
                              className="flex items-start space-x-2 p-2 bg-background rounded border"
                            >
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-sm">{suggestion}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Monitor className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">选择截图进行预览</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  点击左侧截图列表中的任意截图来查看详情
                </p>
                <button
                  onClick={captureDesktop}
                  disabled={isCapturing || !config.desktop?.enabled}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mx-auto"
                >
                  {isCapturing ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Camera className="h-4 w-4" />
                  )}
                  <span>{isCapturing ? '捕获中...' : '立即截图'}</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DesktopRecognition;