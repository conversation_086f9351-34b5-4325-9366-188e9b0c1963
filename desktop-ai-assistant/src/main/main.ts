import { app, <PERSON><PERSON><PERSON>W<PERSON>ow, ipc<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ray, nativeImage } from 'electron';
import { join } from 'path';
import { Logger } from '../utils/logger';
import { ConfigService } from '../services/config';
import { WindowManager } from './window-manager';
import { VoiceService } from '../services/voice';
import { AIService } from '../services/ai';
import { MedicalIntegrationService } from '../services/medical-integration';
import { DesktopRecognitionService } from '../services/desktop-recognition';
import { ShortcutService } from '../services/shortcut';
import type { 
  AppConfig, 
  VoiceConfig, 
  AIConfig, 
  MedicalConfig, 
  DesktopRecognitionConfig, 
  ShortcutConfig 
} from '../shared/types';

// 应用程序主类
class DesktopAIAssistant {
  private logger: Logger;
  private configService: ConfigService;
  private windowManager: WindowManager;
  private voiceService: VoiceService;
  private aiService: AIService;
  private medicalService: MedicalIntegrationService;
  private desktopRecognitionService: DesktopRecognitionService;
  private shortcutService: ShortcutService;
  private tray: Tray | null = null;
  private isQuitting = false;
  private isInitialized = false;

  constructor() {
    this.logger = new Logger();
    this.configService = new ConfigService(this.logger);
    
    // 初始化服务（稍后在initialize中完成）
    this.windowManager = null as any;
    this.voiceService = null as any;
    this.aiService = null as any;
    this.medicalService = null as any;
    this.desktopRecognitionService = null as any;
    this.shortcutService = null as any;
  }

  /**
   * 初始化应用程序
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing Desktop AI Assistant...');
    
    try {
      // 设置应用程序事件监听器
      this.setupAppEventListeners();
      
      // 初始化配置服务
      await this.configService.initialize();
      
      // 获取配置
      const config = await this.configService.getConfig();
      this.logger.info('Retrieved config:', { 
        hasVoice: !!config.voice, 
        voiceEnabled: config.voice?.enabled,
        configKeys: Object.keys(config)
      });
      
      // 初始化窗口管理器
      this.windowManager = new WindowManager(this.configService, this.logger);
      await this.windowManager.initialize();
      
      // 初始化语音服务
      console.log('Debug - config.voice:', JSON.stringify(config.voice, null, 2));
      console.log('Debug - config.voice type:', typeof config.voice);
      console.log('Debug - config.voice.enabled:', config.voice?.enabled);
      
      if (!config.voice) {
        throw new Error('config.voice is undefined');
      }
      
      this.voiceService = new VoiceService(config.voice as VoiceConfig, this.logger);
      
      // 初始化AI服务
      this.aiService = new AIService(config.ai as AIConfig, this.logger);
      await this.aiService.initialize();
      
      // 初始化医疗集成服务
      this.medicalService = new MedicalIntegrationService(config.medical as MedicalConfig, this.logger);
      await this.medicalService.initialize();
      
      // 初始化桌面识别服务
      this.desktopRecognitionService = new DesktopRecognitionService(
        config.desktopRecognition as DesktopRecognitionConfig, 
        this.logger
      );
      await this.desktopRecognitionService.initialize();
      
      // 初始化快捷键服务
      this.shortcutService = new ShortcutService(
        config.shortcuts as ShortcutConfig,
        this.logger,
        {
          windowManager: this.windowManager,
          voiceService: this.voiceService,
          aiService: this.aiService,
          desktopRecognitionService: this.desktopRecognitionService
        }
      );
      await this.shortcutService.initialize();
      
      // 设置服务间的事件监听
      this.setupServiceEventListeners();
      
      // 设置IPC处理器
      this.setupIPCHandlers();
      
      // 创建系统托盘
      this.createTray();
      
      // 设置应用菜单
      this.setupApplicationMenu();
      
      // 根据配置决定是否显示主窗口
      if (config.startup?.showFloatingWindow) {
        this.windowManager.showMainWindow();
      }
      
      this.isInitialized = true;
      this.logger.info('Desktop AI Assistant initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Desktop AI Assistant:', error);
      throw error;
    }
  }

  /**
   * 设置应用程序事件监听器
   */
  private setupAppEventListeners(): void {
    // 当所有窗口关闭时
    app.on('window-all-closed', () => {
      // 在macOS上，除非用户明确退出，否则应用程序保持活动状态
      if (process.platform !== 'darwin') {
        this.quit();
      }
    });

    // 当应用程序激活时（macOS）
    app.on('activate', () => {
      if (this.isInitialized && BrowserWindow.getAllWindows().length === 0) {
        this.windowManager.showMainWindow();
      }
    });

    // 应用程序即将退出
    app.on('before-quit', (event) => {
      if (!this.isQuitting) {
        event.preventDefault();
        this.quit();
      }
    });

    // 处理第二个实例
    app.on('second-instance', () => {
      // 如果用户尝试打开另一个实例，聚焦到主窗口
      if (this.windowManager) {
        this.windowManager.showMainWindow();
        const mainWindow = this.windowManager.getMainWindow();
        if (mainWindow) {
          if (mainWindow.isMinimized()) {
            mainWindow.restore();
          }
          mainWindow.focus();
        }
      }
    });

    // 处理协议URL（如果需要）
    app.setAsDefaultProtocolClient('desktop-ai-assistant');
  }

  /**
   * 设置服务间的事件监听
   */
  private setupServiceEventListeners(): void {
    // 语音识别结果处理
    this.voiceService.on('recognition-result', async (result: string) => {
      this.logger.info(`Voice recognition result: ${result}`);
      
      // 将语音识别结果发送给AI服务处理
      try {
        const aiMessage = {
          role: 'user' as const,
          content: result,
          timestamp: Date.now()
        };
        const response = await this.aiService.processMessage(aiMessage);
        
        // 将AI响应转换为语音
        if (response && this.voiceService) {
          try {
            const voiceState = this.voiceService.getState();
            if (voiceState.synthesisState === 'idle') {
              await this.voiceService.speak(response.content);
            }
          } catch (error) {
            this.logger.warn('Failed to get voice state or speak response:', error);
          }
        }
        
        // 发送结果到渲染进程
        this.broadcastToRenderers('voice-recognition-result', { 
          input: result, 
          response 
        });
      } catch (error) {
        this.logger.error('Error processing voice recognition result:', error);
      }
    });

    // 桌面识别结果处理
    this.desktopRecognitionService.on('analysis-complete', (analysis: any) => {
      this.logger.info('Desktop analysis completed');
      
      // 发送分析结果到渲染进程
      this.broadcastToRenderers('desktop-analysis-result', analysis);
    });

    // AI服务响应处理
    this.aiService.on('response', (response: string) => {
      this.logger.info('AI service response received');
      
      // 发送AI响应到渲染进程
      this.broadcastToRenderers('ai-response', response);
    });

    // 医疗服务搜索结果处理
    this.medicalService.on('search-complete', (results: any) => {
      this.logger.info('Medical search completed');
      
      // 发送搜索结果到渲染进程
      this.broadcastToRenderers('medical-search-result', results);
    });

    // 快捷键触发处理
    this.shortcutService.on('shortcut-triggered', (action: any, shortcut: string) => {
      this.logger.info(`Shortcut triggered: ${shortcut}`);
      
      // 发送快捷键事件到渲染进程
      this.broadcastToRenderers('shortcut-triggered', { action, shortcut });
    });

    // 配置变化处理 - 注释掉，因为 ConfigService 可能没有事件发射器
    // this.configService.on('config-changed', async (key: string, value: any) => {
    //   this.logger.info(`Configuration changed: ${key}`);
    //   
    //   // 根据配置变化更新相应服务
    //   await this.handleConfigChange(key, value);
    //   
    //   // 发送配置变化到渲染进程
    //   this.broadcastToRenderers('config-changed', { key, value });
    // });
  }

  /**
   * 设置IPC处理器
   */
  private setupIPCHandlers(): void {
    // 获取应用状态
    ipcMain.handle('get-app-state', () => {
      try {
        return {
          voice: this.voiceService?.getState() || null,
          ai: this.aiService?.getState() || null,
          medical: this.medicalService?.getState() || null,
          desktop: this.desktopRecognitionService?.getState() || null,
          shortcuts: this.shortcutService?.getState() || null,
          config: this.configService.getConfig()
        };
      } catch (error) {
        this.logger.error('Error getting app state:', error);
        return {
          voice: null,
          ai: null,
          medical: null,
          desktop: null,
          shortcuts: null,
          config: this.configService.getConfig()
        };
      }
    });

    // 语音控制
    ipcMain.handle('voice-start-recognition', async () => {
      return await this.voiceService.startRecognition();
    });

    ipcMain.handle('voice-stop-recognition', async () => {
      return await this.voiceService.stopRecognition();
    });

    ipcMain.handle('voice-speak', async (_, text: string) => {
      return await this.voiceService.speak(text);
    });

    ipcMain.handle('voice-stop-speaking', async () => {
      return await this.voiceService.stopSpeaking();
    });

    // AI服务控制
    ipcMain.handle('ai-process-message', async (_, message: string) => {
      const aiMessage = {
        role: 'user' as const,
        content: message,
        timestamp: Date.now()
      };
      const response = await this.aiService.processMessage(aiMessage);
      return response.content;
    });

    ipcMain.handle('ai-clear-history', async () => {
      return this.aiService.clearHistory();
    });

    ipcMain.handle('ai-generate-summary', async (_, text: string) => {
      return await this.aiService.generateSummary(text);
    });

    // 桌面识别控制
    ipcMain.handle('desktop-capture-screen', async (_, options: any) => {
      return await this.desktopRecognitionService.captureScreen(options);
    });

    ipcMain.handle('desktop-analyze-screen', async (_, options: any) => {
      return await this.desktopRecognitionService.captureAndAnalyze(options);
    });

    ipcMain.handle('desktop-get-displays', () => {
      return this.desktopRecognitionService.getAvailableDisplays();
    });

    // 医疗服务控制
    ipcMain.handle('medical-search-patients', async (_, query: string, options: any) => {
      return await this.medicalService.searchPatients(query, options);
    });

    ipcMain.handle('medical-get-patient', async (_, patientId: string) => {
      return await this.medicalService.getPatientRecord(patientId);
    });

    // 配置管理
    ipcMain.handle('config-get', (_, key?: string) => {
      return key ? this.configService.getConfig() : this.configService.getConfig();
    });

    ipcMain.handle('config-set', async (_, key: string, value: any) => {
      return this.configService.updateConfig({ [key]: value });
    });

    ipcMain.handle('config-update', async (_, config: Partial<AppConfig>) => {
      return this.configService.updateConfig(config);
    });

    ipcMain.handle('config-reset', async () => {
      return this.configService.initialize();
    });

    // 窗口控制
    ipcMain.handle('window-show-main', () => {
      this.windowManager.showMainWindow();
    });

    ipcMain.handle('window-hide-main', () => {
      this.windowManager.hideMainWindow();
    });

    ipcMain.handle('window-toggle-main', () => {
      this.windowManager.toggleMainWindow();
    });

    ipcMain.handle('window-show-floating', () => {
      this.windowManager.showFloatingWindow();
    });

    ipcMain.handle('window-hide-floating', () => {
      this.windowManager.hideFloatingWindow();
    });

    ipcMain.handle('window-show-voice', () => {
      this.windowManager.showVoiceWindow();
    });

    // 应用控制
    ipcMain.handle('app-quit', () => {
      this.quit();
    });

    ipcMain.handle('app-minimize-to-tray', () => {
      this.windowManager.hideMainWindow();
    });

    ipcMain.handle('app-get-version', () => {
      return app.getVersion();
    });
  }

  /**
   * 处理配置变化
   */
  // private async handleConfigChange(key: string, value: any): Promise<void> {
  //   try {
  //     if (key.startsWith('voice.')) {
  //       await this.voiceService.updateConfig(value);
  //     } else if (key.startsWith('ai.')) {
  //       await this.aiService.updateConfig(value);
  //     } else if (key.startsWith('medical.')) {
  //       await this.medicalService.updateConfig(value);
  //     } else if (key.startsWith('desktopRecognition.')) {
  //       await this.desktopRecognitionService.updateConfig(value);
  //     } else if (key.startsWith('shortcuts.')) {
  //       await this.shortcutService.updateConfig(value);
  //     }
  //   } catch (error) {
  //     this.logger.error(`Error handling config change for ${key}:`, error);
  //   }
  // }

  /**
   * 广播消息到所有渲染进程
   */
  private broadcastToRenderers(channel: string, data: any): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(channel, data);
      }
    });
  }

  /**
   * 创建系统托盘
   */
  private createTray(): void {
    try {
      const iconPath = join(__dirname, '../../assets/icons/tray-icon.png');
      const trayIcon = nativeImage.createFromPath(iconPath);
      
      this.tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));
      this.tray.setToolTip('Desktop AI Assistant');
      
      const contextMenu = Menu.buildFromTemplate([
        {
          label: '显示主窗口',
          click: () => this.windowManager.showMainWindow()
        },
        {
          label: '显示浮动窗口',
          click: () => this.windowManager.showFloatingWindow()
        },
        {
          label: '语音助手',
          click: () => this.windowManager.showVoiceWindow()
        },
        { type: 'separator' },
        {
          label: '开始语音识别',
          click: async () => {
            try {
              await this.voiceService.startRecognition();
            } catch (error) {
              this.logger.error('Failed to start voice recognition from tray:', error);
            }
          }
        },
        {
          label: '停止语音识别',
          click: async () => {
            try {
              await this.voiceService.stopRecognition();
            } catch (error) {
              this.logger.error('Failed to stop voice recognition from tray:', error);
            }
          }
        },
        { type: 'separator' },
        {
          label: '设置',
          click: () => {
            this.windowManager.showMainWindow();
            // 发送消息到渲染进程显示设置页面
            const mainWindow = this.windowManager.getMainWindow();
            if (mainWindow) {
              mainWindow.webContents.send('navigate-to', '/settings');
            }
          }
        },
        {
          label: '退出',
          click: () => this.quit()
        }
      ]);
      
      this.tray.setContextMenu(contextMenu);
      
      // 双击托盘图标显示主窗口
      this.tray.on('double-click', () => {
        this.windowManager.toggleMainWindow();
      });
      
      this.logger.info('System tray created');
    } catch (error) {
      this.logger.warn('Failed to create system tray:', error);
    }
  }

  /**
   * 设置应用程序菜单
   */
  private setupApplicationMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: '应用',
        submenu: [
          {
            label: '关于 Desktop AI Assistant',
            role: 'about'
          },
          { type: 'separator' },
          {
            label: '设置',
            accelerator: 'CmdOrCtrl+,',
            click: () => {
              this.windowManager.showMainWindow();
              const mainWindow = this.windowManager.getMainWindow();
              if (mainWindow) {
                mainWindow.webContents.send('navigate-to', '/settings');
              }
            }
          },
          { type: 'separator' },
          {
            label: '隐藏应用',
            accelerator: 'CmdOrCtrl+H',
            role: 'hide'
          },
          {
            label: '隐藏其他',
            accelerator: 'CmdOrCtrl+Shift+H',
            role: 'hideOthers'
          },
          {
            label: '显示全部',
            role: 'unhide'
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: 'CmdOrCtrl+Q',
            click: () => this.quit()
          }
        ]
      },
      {
        label: '编辑',
        submenu: [
          { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
          { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
          { type: 'separator' },
          { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
          { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
          { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' },
          { label: '全选', accelerator: 'CmdOrCtrl+A', role: 'selectAll' }
        ]
      },
      {
        label: '窗口',
        submenu: [
          {
            label: '主窗口',
            accelerator: 'CmdOrCtrl+1',
            click: () => this.windowManager.toggleMainWindow()
          },
          {
            label: '浮动窗口',
            accelerator: 'CmdOrCtrl+2',
            click: () => this.windowManager.toggleFloatingWindow()
          },
          {
            label: '语音窗口',
            accelerator: 'CmdOrCtrl+3',
            click: () => this.windowManager.showVoiceWindow()
          },
          { type: 'separator' },
          { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
          { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' }
        ]
      },
      {
        label: '帮助',
        submenu: [
          {
            label: '学习更多',
            click: async () => {
              const { shell } = require('electron');
              await shell.openExternal('https://github.com/your-repo/desktop-ai-assistant');
            }
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  /**
   * 退出应用程序
   */
  async quit(): Promise<void> {
    if (this.isQuitting) {
      return;
    }
    
    this.isQuitting = true;
    this.logger.info('Quitting Desktop AI Assistant...');
    
    try {
      // 清理所有服务
      if (this.shortcutService) {
        await this.shortcutService.cleanup();
      }
      
      if (this.desktopRecognitionService) {
        await this.desktopRecognitionService.cleanup();
      }
      
      if (this.medicalService) {
        await this.medicalService.cleanup();
      }
      
      if (this.aiService) {
        await this.aiService.cleanup();
      }
      
      if (this.voiceService) {
        await this.voiceService.cleanup();
      }
      
      if (this.windowManager) {
        await this.windowManager.cleanup();
      }
      
      // 销毁托盘
      if (this.tray) {
        this.tray.destroy();
        this.tray = null;
      }
      
      this.logger.info('Desktop AI Assistant cleanup completed');
      
      // 退出应用
      app.quit();
    } catch (error) {
      this.logger.error('Error during application cleanup:', error);
      app.quit();
    }
  }
}

// 应用程序入口点
console.log('Creating DesktopAIAssistant instance...');
const desktopAIAssistant = new DesktopAIAssistant();

// 确保只有一个实例运行
console.log('Requesting single instance lock...');
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log('Another instance is already running, quitting...');
  app.quit();
} else {
  console.log('Got single instance lock, waiting for app ready...');
  
  // 当应用程序准备就绪时初始化
  app.whenReady().then(async () => {
    try {
      console.log('App is ready, starting initialization...');
      await desktopAIAssistant.initialize();
      console.log('Application initialized successfully');
    } catch (error) {
      console.error('Failed to initialize application:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace available');
      // 不要立即退出，让用户看到错误信息
      setTimeout(() => {
        app.quit();
      }, 5000);
    }
  });
}

// 导出应用实例（用于测试）
export { desktopAIAssistant };