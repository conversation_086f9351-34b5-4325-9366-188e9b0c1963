import { BrowserWindow, screen, nativeImage } from 'electron';
import { join } from 'path';
import type { ConfigService } from '../services/config';
import type { Logger } from '../utils/logger';
import type { WindowMode } from '../shared/types';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private floatingWindow: BrowserWindow | null = null;
  private voiceWindow: BrowserWindow | null = null;
  private configService: ConfigService;
  private logger: Logger;
  private isDev: boolean;

  constructor(configService: ConfigService, logger: Logger) {
    this.configService = configService;
    this.logger = logger;
    this.isDev = process.env.NODE_ENV === 'development';
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing window manager...');

    // 预创建窗口以提高响应速度
    await this.createMainWindow();
    await this.createFloatingWindow();

    this.logger.info('Window manager initialized');
  }

  // 创建主窗口
  private async createMainWindow(): Promise<void> {
    const config = await this.configService.getConfig();
    const windowConfig = config.windows.main;

    // 获取屏幕尺寸
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

    // 计算窗口位置
    const width = Math.min(windowConfig.width, screenWidth - 100);
    const height = Math.min(windowConfig.height, screenHeight - 100);
    const x = Math.floor((screenWidth - width) / 2);
    const y = Math.floor((screenHeight - height) / 2);

    this.mainWindow = new BrowserWindow({
      width,
      height,
      x,
      y,
      minWidth: 800,
      minHeight: 600,
      show: false, // 初始不显示
      frame: false, // 无边框
      titleBarStyle: 'hidden',
      trafficLightPosition: { x: 16, y: 16 },
      transparent: false,
      hasShadow: true,
      resizable: true,
      maximizable: true,
      minimizable: true,
      closable: true,
      alwaysOnTop: false,
      skipTaskbar: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: true, // 始终启用 webSecurity
        allowRunningInsecureContent: false, // 禁用不安全内容
        experimentalFeatures: false,
        nodeIntegrationInWorker: false,
        nodeIntegrationInSubFrames: false,
        safeDialogs: true,
        safeDialogsMessage: '此应用程序正在尝试显示多个对话框。',
        spellcheck: false
      },
      icon: this.getAppIcon()
    });

    // 加载页面
    await this.loadWindow(this.mainWindow, 'main');

    // 设置窗口事件
    this.setupMainWindowEvents();

    this.logger.info('Main window created');
  }

  // 创建浮动窗口
  private async createFloatingWindow(): Promise<void> {
    // 获取鼠标位置附近的屏幕
    const cursorPoint = screen.getCursorScreenPoint();
    const currentDisplay = screen.getDisplayNearestPoint(cursorPoint);
    const { width: screenWidth, height: screenHeight } = currentDisplay.workAreaSize;

    // 浮动窗口尺寸
    const width = 320;
    const height = 480;
    const x = Math.min(cursorPoint.x - width / 2, screenWidth - width - 20);
    const y = Math.min(cursorPoint.y - height / 2, screenHeight - height - 20);

    this.floatingWindow = new BrowserWindow({
      width,
      height,
      x: Math.max(x, 20),
      y: Math.max(y, 20),
      minWidth: 280,
      minHeight: 200,
      maxWidth: 400,
      maxHeight: 600,
      show: false,
      frame: false,
      titleBarStyle: 'hidden',
      transparent: true,
      hasShadow: true,
      resizable: true,
      maximizable: false,
      minimizable: false,
      closable: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      focusable: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: true, // 始终启用 webSecurity
        allowRunningInsecureContent: false, // 禁用不安全内容
        experimentalFeatures: false,
        nodeIntegrationInWorker: false,
        nodeIntegrationInSubFrames: false,
        safeDialogs: true,
        safeDialogsMessage: '此应用程序正在尝试显示多个对话框。',
        spellcheck: false
      },
      icon: this.getAppIcon()
    });

    // 加载页面
    await this.loadWindow(this.floatingWindow, 'floating');

    // 设置窗口事件
    this.setupFloatingWindowEvents();

    this.logger.info('Floating window created');
  }

  // 创建语音窗口
  private async createVoiceWindow(): Promise<void> {
    if (this.voiceWindow) {
      return;
    }

    // 获取屏幕中心位置
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

    const width = 400;
    const height = 300;
    const x = Math.floor((screenWidth - width) / 2);
    const y = Math.floor((screenHeight - height) / 2);

    this.voiceWindow = new BrowserWindow({
      width,
      height,
      x,
      y,
      minWidth: 350,
      minHeight: 250,
      maxWidth: 500,
      maxHeight: 400,
      show: false,
      frame: false,
      titleBarStyle: 'hidden',
      transparent: true,
      hasShadow: true,
      resizable: false,
      maximizable: false,
      minimizable: false,
      closable: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      focusable: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: true, // 始终启用 webSecurity
        allowRunningInsecureContent: false, // 禁用不安全内容
        experimentalFeatures: false,
        nodeIntegrationInWorker: false,
        nodeIntegrationInSubFrames: false,
        safeDialogs: true,
        safeDialogsMessage: '此应用程序正在尝试显示多个对话框。',
        spellcheck: false
      },
      icon: this.getAppIcon()
    });

    // 加载页面
    await this.loadWindow(this.voiceWindow, 'voice');

    // 设置窗口事件
    this.setupVoiceWindowEvents();

    this.logger.info('Voice window created');
  }

  // 加载窗口内容
  private async loadWindow(window: BrowserWindow, mode: WindowMode): Promise<void> {
    const url = this.isDev
      ? `http://127.0.0.1:5173?mode=${mode}`
      : `file://${join(__dirname, '../renderer/index.html')}?mode=${mode}`;

    try {
      await window.loadURL(url);

      if (this.isDev) {
        // 开发模式下打开开发者工具
        window.webContents.openDevTools({ mode: 'detach' });
      }
    } catch (error) {
      this.logger.error(`Failed to load window (${mode}):`, error);
      throw error;
    }
  }

  // 设置主窗口事件
  private setupMainWindowEvents(): void {
    if (!this.mainWindow) return;

    this.mainWindow.on('ready-to-show', () => {
      this.logger.info('Main window ready to show');
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      this.logger.info('Main window closed');
    });

    this.mainWindow.on('focus', () => {
      this.logger.debug('Main window focused');
    });

    this.mainWindow.on('blur', () => {
      this.logger.debug('Main window blurred');
    });

    this.mainWindow.on('minimize', () => {
      this.logger.debug('Main window minimized');
    });

    this.mainWindow.on('restore', () => {
      this.logger.debug('Main window restored');
    });

    // 阻止窗口关闭，改为隐藏
    this.mainWindow.on('close', (event) => {
      event.preventDefault();
      this.mainWindow?.hide();
    });
  }

  // 设置浮动窗口事件
  private setupFloatingWindowEvents(): void {
    if (!this.floatingWindow) return;

    this.floatingWindow.on('ready-to-show', () => {
      this.logger.info('Floating window ready to show');
    });

    this.floatingWindow.on('closed', () => {
      this.floatingWindow = null;
      this.logger.info('Floating window closed');
    });

    this.floatingWindow.on('blur', () => {
      // 浮动窗口失去焦点时自动隐藏
      setTimeout(() => {
        if (this.floatingWindow && !this.floatingWindow.isFocused()) {
          this.floatingWindow.hide();
        }
      }, 200);
    });

    // 阻止窗口关闭，改为隐藏
    this.floatingWindow.on('close', (event) => {
      event.preventDefault();
      this.floatingWindow?.hide();
    });
  }

  // 设置语音窗口事件
  private setupVoiceWindowEvents(): void {
    if (!this.voiceWindow) return;

    this.voiceWindow.on('ready-to-show', () => {
      this.logger.info('Voice window ready to show');
    });

    this.voiceWindow.on('closed', () => {
      this.voiceWindow = null;
      this.logger.info('Voice window closed');
    });

    // 语音窗口可以真正关闭
    this.voiceWindow.on('close', () => {
      this.voiceWindow = null;
    });
  }

  /**
   * 获取应用图标
   */
  private getAppIcon(): Electron.NativeImage {
    const iconPaths = [
      join(__dirname, '../../assets/icon.png'),
      join(__dirname, '../../assets/icon.ico'),
      join(__dirname, '../../assets/app-icon.png'),
      join(process.resourcesPath, 'icon.png'),
      join(process.resourcesPath, 'icon.ico')
    ];

    for (const iconPath of iconPaths) {
      try {
        if (require('fs').existsSync(iconPath)) {
          return nativeImage.createFromPath(iconPath);
        }
      } catch (error) {
        // 忽略错误，继续尝试下一个路径
      }
    }

    // 如果没有找到图标文件，返回空的 NativeImage
    return nativeImage.createEmpty();
  }

  // 公共方法
  async showMainWindow(): Promise<void> {
    if (!this.mainWindow) {
      await this.createMainWindow();
    }

    if (this.mainWindow) {
      this.mainWindow.show();
      this.mainWindow.focus();
      this.logger.info('Main window shown');
    }
  }

  async showFloatingWindow(): Promise<void> {
    if (!this.floatingWindow) {
      await this.createFloatingWindow();
    }

    if (this.floatingWindow) {
      // 更新位置到鼠标附近
      const cursorPoint = screen.getCursorScreenPoint();
      const currentDisplay = screen.getDisplayNearestPoint(cursorPoint);
      const { width: screenWidth, height: screenHeight } = currentDisplay.workAreaSize;

      const [windowWidth, windowHeight] = this.floatingWindow.getSize();
      const x = Math.min(cursorPoint.x - windowWidth / 2, screenWidth - windowWidth - 20);
      const y = Math.min(cursorPoint.y - windowHeight / 2, screenHeight - windowHeight - 20);

      this.floatingWindow.setPosition(Math.max(x, 20), Math.max(y, 20));
      this.floatingWindow.show();
      this.floatingWindow.focus();
      this.logger.info('Floating window shown');
    }
  }

  async showVoiceWindow(): Promise<void> {
    if (!this.voiceWindow) {
      await this.createVoiceWindow();
    }

    if (this.voiceWindow) {
      this.voiceWindow.show();
      this.voiceWindow.focus();
      this.logger.info('Voice window shown');
    }
  }

  hideMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.hide();
      this.logger.info('Main window hidden');
    }
  }

  hideFloatingWindow(): void {
    if (this.floatingWindow) {
      this.floatingWindow.hide();
      this.logger.info('Floating window hidden');
    }
  }

  hideVoiceWindow(): void {
    if (this.voiceWindow) {
      this.voiceWindow.hide();
      this.logger.info('Voice window hidden');
    }
  }

  toggleMainWindow(): void {
    if (this.mainWindow?.isVisible()) {
      this.hideMainWindow();
    } else {
      this.showMainWindow();
    }
  }

  toggleFloatingWindow(): void {
    if (this.floatingWindow?.isVisible()) {
      this.hideFloatingWindow();
    } else {
      this.showFloatingWindow();
    }
  }

  // 获取窗口实例
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  getFloatingWindow(): BrowserWindow | null {
    return this.floatingWindow;
  }

  getVoiceWindow(): BrowserWindow | null {
    return this.voiceWindow;
  }

  // 清理资源
  async cleanup(): Promise<void> {
    this.logger.info('Cleaning up window manager...');

    if (this.mainWindow) {
      this.mainWindow.removeAllListeners();
      this.mainWindow.destroy();
      this.mainWindow = null;
    }

    if (this.floatingWindow) {
      this.floatingWindow.removeAllListeners();
      this.floatingWindow.destroy();
      this.floatingWindow = null;
    }

    if (this.voiceWindow) {
      this.voiceWindow.removeAllListeners();
      this.voiceWindow.destroy();
      this.voiceWindow = null;
    }

    this.logger.info('Window manager cleanup completed');
  }
}