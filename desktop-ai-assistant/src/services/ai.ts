import { EventEmitter } from 'events';
import type { Logger } from '../utils/logger';
import type { AIConfig, AIState, AIMessage, AIResponse } from '../shared/types';

// AI服务状态
export type AIServiceState = 'idle' | 'processing' | 'error';

// AI事件
export interface AIEvents {
  'processing-start': () => void;
  'processing-end': () => void;
  'processing-error': (error: Error) => void;
  'response-received': (response: AIResponse) => void;
  'state-change': (state: AIState) => void;
}

// OpenAI API响应接口
interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Claude API响应接口
interface ClaudeResponse {
  content: Array<{
    text: string;
    type: string;
  }>;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

export class AIService extends EventEmitter {
  private logger: Logger;
  private config: AIConfig;
  private state: AIServiceState = 'idle';
  private isEnabled: boolean = false;
  private conversationHistory: AIMessage[] = [];
  private requestQueue: Array<() => Promise<void>> = [];
  private isProcessingQueue: boolean = false;
  private rateLimitDelay: number = 1000; // 1秒
  private lastRequestTime: number = 0;

  constructor(config: AIConfig, logger: Logger) {
    super();
    this.config = config;
    this.logger = logger;
    this.isEnabled = config.enabled;
  }

  /**
   * 初始化AI服务
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing AI service...');
    
    try {
      if (!this.isEnabled) {
        this.logger.info('AI service is disabled');
        return;
      }

      // 验证API密钥
      await this.validateApiKey();
      
      // 测试连接
      await this.testConnection();
      
      this.logger.info('AI service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize AI service:', error);
      throw error;
    }
  }

  /**
   * 验证API密钥
   */
  private async validateApiKey(): Promise<void> {
    const apiKey = this.getApiKey();
    if (!apiKey) {
      throw new Error(`API key is required for ${this.config.provider}`);
    }
    
    if (apiKey.startsWith('sk-') && apiKey.length < 20) {
      throw new Error('Invalid API key format');
    }
    
    this.logger.info(`API key validated for ${this.config.provider}`);
  }

  /**
   * 获取API密钥
   */
  private getApiKey(): string {
    return this.config.apiKey || '';
  }

  /**
   * 获取API端点
   */
  private getApiEndpoint(): string {
    switch (this.config.provider) {
      case 'openai':
        return this.config.apiUrl || 'https://api.openai.com/v1/chat/completions';
      case 'claude':
        return 'https://api.anthropic.com/v1/messages';
      case 'gemini':
        return `https://generativelanguage.googleapis.com/v1beta/models/${this.config.model}:generateContent`;
      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<void> {
    try {
      const testMessage: AIMessage = {
        role: 'user',
        content: 'Hello, this is a connection test.',
        timestamp: Date.now()
      };
      
      await this.processMessage(testMessage, { skipHistory: true });
      this.logger.info('AI service connection test passed');
    } catch (error) {
      this.logger.error('AI service connection test failed:', error);
      throw new Error(`Failed to connect to ${this.config.provider}: ${error}`);
    }
  }

  /**
   * 处理消息
   */
  async processMessage(
    message: AIMessage, 
    options: { skipHistory?: boolean; systemPrompt?: string } = {}
  ): Promise<AIResponse> {
    if (!this.isEnabled) {
      throw new Error('AI service is not enabled');
    }
    
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const response = await this.executeRequest(message, options);
          resolve(response);
        } catch (error) {
          reject(error);
        }
      });
      
      this.processQueue();
    });
  }

  /**
   * 处理请求队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }
    
    this.isProcessingQueue = true;
    
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        // 实施速率限制
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.rateLimitDelay) {
          await new Promise(resolve => 
            setTimeout(resolve, this.rateLimitDelay - timeSinceLastRequest)
          );
        }
        
        await request();
        this.lastRequestTime = Date.now();
      }
    }
    
    this.isProcessingQueue = false;
  }

  /**
   * 执行请求
   */
  private async executeRequest(
    message: AIMessage, 
    options: { skipHistory?: boolean; systemPrompt?: string } = {}
  ): Promise<AIResponse> {
    this.state = 'processing';
    this.emit('processing-start');
    this.emitStateChange();
    
    try {
      // 添加到对话历史
      if (!options.skipHistory) {
        this.conversationHistory.push(message);
        
        // 限制历史长度
        if (this.conversationHistory.length > this.config.maxHistory) {
          this.conversationHistory = this.conversationHistory.slice(-this.config.maxHistory);
        }
      }
      
      // 构建请求
      const requestBody = this.buildRequestBody(message, options.systemPrompt);
      
      // 发送请求
      const response = await this.sendRequest(requestBody);
      
      // 解析响应
      const aiResponse = this.parseResponse(response);
      
      // 添加助手回复到历史
      if (!options.skipHistory) {
        this.conversationHistory.push({
          role: 'assistant',
          content: aiResponse.content,
          timestamp: Date.now()
        });
      }
      
      this.state = 'idle';
      this.emit('processing-end');
      this.emit('response-received', aiResponse);
      this.emitStateChange();
      
      this.logger.info(`AI response generated (${aiResponse.usage?.totalTokens || 0} tokens)`);
      
      return aiResponse;
    } catch (error) {
      this.state = 'error';
      this.emit('processing-error', error as Error);
      this.emitStateChange();
      this.logger.error('Failed to process AI message:', error);
      throw error;
    }
  }

  /**
   * 构建请求体
   */
  private buildRequestBody(message: AIMessage, systemPrompt?: string): any {
    const messages = [...this.conversationHistory, message];
    
    // 添加系统提示
    if (systemPrompt) {
      messages.unshift({
        role: 'system',
        content: systemPrompt,
        timestamp: Date.now()
      });
    }
    
    switch (this.config.provider) {
      case 'openai':
        return {
          model: this.config.model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          top_p: this.config.topP,
          frequency_penalty: this.config.frequencyPenalty,
          presence_penalty: this.config.presencePenalty,
          stream: false
        };
        
      case 'claude':
        return {
          model: this.config.model,
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          top_p: this.config.topP,
          messages: messages.filter(msg => msg.role !== 'system').map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          system: systemPrompt || undefined
        };
        
      case 'gemini':
        return {
          contents: messages.map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
          })),
          generationConfig: {
            maxOutputTokens: this.config.maxTokens,
            temperature: this.config.temperature,
            topP: this.config.topP
          }
        };
        
      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest(requestBody: any): Promise<any> {
    const apiKey = this.getApiKey();
    const endpoint = this.getApiEndpoint();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    // 设置认证头
    switch (this.config.provider) {
      case 'openai':
        headers['Authorization'] = `Bearer ${apiKey}`;
        break;
      case 'claude':
        headers['x-api-key'] = apiKey;
        headers['anthropic-version'] = '2023-06-01';
        break;
      case 'gemini':
        // Gemini使用查询参数
        break;
    }
    
    const url = this.config.provider === 'gemini' 
      ? `${endpoint}?key=${apiKey}`
      : endpoint;
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    return await response.json();
  }

  /**
   * 解析响应
   */
  private parseResponse(response: any): AIResponse {
    switch (this.config.provider) {
      case 'openai':
        return this.parseOpenAIResponse(response as OpenAIResponse);
      case 'claude':
        return this.parseClaudeResponse(response as ClaudeResponse);
      case 'gemini':
        return this.parseGeminiResponse(response);
      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  /**
   * 解析OpenAI响应
   */
  private parseOpenAIResponse(response: OpenAIResponse): AIResponse {
    const choice = response.choices[0];
    if (!choice) {
      throw new Error('No response from OpenAI');
    }
    
    return {
      content: choice.message.content,
      provider: 'openai',
      model: this.config.model,
      usage: {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens
      },
      timestamp: Date.now()
    };
  }

  /**
   * 解析Claude响应
   */
  private parseClaudeResponse(response: ClaudeResponse): AIResponse {
    const content = response.content[0];
    if (!content || content.type !== 'text') {
      throw new Error('No text response from Claude');
    }
    
    return {
      content: content.text,
      provider: 'claude',
      model: this.config.model,
      usage: {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens
      },
      timestamp: Date.now()
    };
  }

  /**
   * 解析Gemini响应
   */
  private parseGeminiResponse(response: any): AIResponse {
    const candidate = response.candidates?.[0];
    if (!candidate || !candidate.content) {
      throw new Error('No response from Gemini');
    }
    
    const text = candidate.content.parts?.[0]?.text;
    if (!text) {
      throw new Error('No text in Gemini response');
    }
    
    return {
      content: text,
      provider: 'gemini',
      model: this.config.model,
      usage: {
        promptTokens: response.usageMetadata?.promptTokenCount || 0,
        completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.usageMetadata?.totalTokenCount || 0
      },
      timestamp: Date.now()
    };
  }

  /**
   * 生成摘要
   */
  async generateSummary(text: string, maxLength: number = 200): Promise<string> {
    const summaryPrompt = `请为以下文本生成一个简洁的摘要，长度不超过${maxLength}个字符：\n\n${text}`;
    
    const message: AIMessage = {
      role: 'user',
      content: summaryPrompt,
      timestamp: Date.now()
    };
    
    const response = await this.processMessage(message, { skipHistory: true });
    return response.content;
  }

  /**
   * 分析桌面内容
   */
  async analyzeDesktopContent(
    content: string, 
    context: string = ''
  ): Promise<AIResponse> {
    const analysisPrompt = `作为桌面AI助手，请分析以下桌面内容并提供有用的建议或操作：

桌面内容：
${content}

${context ? `上下文：${context}` : ''}

请提供：
1. 内容分析
2. 可能的操作建议
3. 相关信息或提醒`;
    
    const message: AIMessage = {
      role: 'user',
      content: analysisPrompt,
      timestamp: Date.now()
    };
    
    return await this.processMessage(message);
  }

  /**
   * 清除对话历史
   */
  clearHistory(): void {
    this.conversationHistory = [];
    this.logger.info('Conversation history cleared');
  }

  /**
   * 获取对话历史
   */
  getHistory(): AIMessage[] {
    return [...this.conversationHistory];
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<AIConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    this.isEnabled = this.config.enabled;
    
    // 如果更改了提供商或API密钥，重新验证
    if (config.provider || config.apiKey) {
      await this.validateApiKey();
    }
    
    this.logger.info('AI service config updated');
    this.emitStateChange();
  }

  /**
   * 获取当前状态
   */
  getState(): AIState {
    return {
      enabled: this.isEnabled,
      state: this.state,
      provider: this.config.provider,
      model: this.config.model,
      historyLength: this.conversationHistory.length,
      queueLength: this.requestQueue.length,
      config: this.config
    };
  }

  /**
   * 获取可用的AI提供商
   */
  getAvailableProviders(): string[] {
    return ['openai', 'claude', 'gemini'];
  }

  /**
   * 发送消息到指定提供商
   */
  async sendMessage(provider: string, messages: AIMessage[]): Promise<AIResponse> {
    // 临时切换提供商
    const originalProvider = this.config.provider;
    this.config.provider = provider as any;
    
    try {
      // 处理最后一条消息
      const lastMessage = messages[messages.length - 1];
      const response = await this.processMessage(lastMessage, { skipHistory: true });
      return response;
    } finally {
      // 恢复原始提供商
      this.config.provider = originalProvider;
    }
  }

  /**
   * 分析内容
   */
  async analyzeContent(content: string, context?: string): Promise<AIResponse> {
    return this.analyzeDesktopContent(content, context || '');
  }

  /**
   * 发送状态变化事件
   */
  private emitStateChange(): void {
    this.emit('state-change', this.getState());
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.logger.info('Cleaning up AI service...');
    
    // 清空请求队列
    this.requestQueue = [];
    this.isProcessingQueue = false;
    
    // 清除对话历史
    this.clearHistory();
    
    // 移除所有监听器
    this.removeAllListeners();
    
    this.logger.info('AI service cleanup completed');
  }
}