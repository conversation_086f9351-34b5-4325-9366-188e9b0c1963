<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Desktop AI Assistant - 浮动窗口" />
    
    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/icons/favicon.svg" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    
    <!-- Theme -->
    <script>
      // 防止闪烁的主题初始化
      (function() {
        const theme = localStorage.getItem('theme') || 'system';
        if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
    
    <title>AI Assistant - 浮动窗口</title>
  </head>
  <body class="bg-transparent font-sans antialiased overflow-hidden">
    <!-- 浮动窗口根节点 -->
    <div id="floating-root" class="w-full h-full"></div>
    
    <!-- 加载指示器 -->
    <div id="floating-loading" class="fixed inset-0 flex items-center justify-center">
      <div class="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">加载中...</span>
        </div>
      </div>
    </div>
    
    <!-- 错误边界 -->
    <div id="floating-error" class="hidden fixed inset-0 flex items-center justify-center">
      <div class="bg-red-50/90 dark:bg-red-900/20 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-xs">
        <div class="flex items-center space-x-2 mb-2">
          <svg class="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span class="text-sm font-medium text-red-800 dark:text-red-200">错误</span>
        </div>
        <p id="floating-error-message" class="text-xs text-red-700 dark:text-red-300 mb-3">浮动窗口遇到错误</p>
        <button
          id="floating-reload"
          class="w-full px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          重新加载
        </button>
      </div>
    </div>
    
    <!-- 脚本 -->
    <script type="module" src="/src/renderer/floating.tsx"></script>
    
    <!-- 错误处理和工具脚本 -->
    <script>
      // 全局错误处理
      window.addEventListener('error', function(event) {
        console.error('Floating window error:', event.error);
        showFloatingError(event.error?.message || '未知错误');
      });
      
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Floating window unhandled rejection:', event.reason);
        showFloatingError(event.reason?.message || '未处理的Promise拒绝');
      });
      
      function showFloatingError(message) {
        const loading = document.getElementById('floating-loading');
        const error = document.getElementById('floating-error');
        const errorMessage = document.getElementById('floating-error-message');
        
        if (loading) loading.style.display = 'none';
        if (error) error.classList.remove('hidden');
        if (errorMessage) errorMessage.textContent = message;
      }
      
      // 重新加载按钮
      document.getElementById('floating-reload')?.addEventListener('click', function() {
        window.location.reload();
      });
      
      // 隐藏加载指示器
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const loading = document.getElementById('floating-loading');
          if (loading && document.getElementById('floating-root').children.length > 0) {
            loading.style.display = 'none';
          }
        }, 500);
      });
      
      // 窗口拖拽功能
      let isDragging = false;
      let dragOffset = { x: 0, y: 0 };
      
      document.addEventListener('mousedown', function(e) {
        // 只有在特定区域才允许拖拽（避免干扰其他交互）
        if (e.target.closest('.drag-handle') || e.target.closest('.floating-header')) {
          isDragging = true;
          dragOffset.x = e.clientX;
          dragOffset.y = e.clientY;
          document.body.style.userSelect = 'none';
        }
      });
      
      document.addEventListener('mousemove', function(e) {
        if (isDragging && window.electronAPI?.window?.moveFloating) {
          const deltaX = e.clientX - dragOffset.x;
          const deltaY = e.clientY - dragOffset.y;
          window.electronAPI.window.moveFloating(deltaX, deltaY);
          dragOffset.x = e.clientX;
          dragOffset.y = e.clientY;
        }
      });
      
      document.addEventListener('mouseup', function() {
        if (isDragging) {
          isDragging = false;
          document.body.style.userSelect = '';
        }
      });
      
      // 防止右键菜单（在生产环境中）
      if (process?.env?.NODE_ENV === 'production') {
        document.addEventListener('contextmenu', function(e) {
          e.preventDefault();
        });
      }
      
      // 键盘快捷键
      document.addEventListener('keydown', function(e) {
        // ESC 键隐藏浮动窗口
        if (e.key === 'Escape' && window.electronAPI?.window?.hideFloating) {
          window.electronAPI.window.hideFloating();
        }
        
        // Ctrl/Cmd + W 关闭浮动窗口
        if ((e.ctrlKey || e.metaKey) && e.key === 'w' && window.electronAPI?.window?.hideFloating) {
          e.preventDefault();
          window.electronAPI.window.hideFloating();
        }
      });
      
      // 窗口焦点管理
      window.addEventListener('blur', function() {
        // 可选：失去焦点时自动隐藏（根据用户设置）
        if (window.electronAPI?.config?.get) {
          window.electronAPI.config.get('floating.hideOnBlur').then(function(hideOnBlur) {
            if (hideOnBlur && window.electronAPI?.window?.hideFloating) {
              window.electronAPI.window.hideFloating();
            }
          }).catch(function() {
            // 忽略配置读取错误
          });
        }
      });
    </script>
  </body>
</html>