# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
release/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Electron specific
app/
package/
*.asar

# macOS specific
*.dmg
*.pkg

# Windows specific
*.exe
*.msi

# Linux specific
*.AppImage
*.deb
*.rpm
*.tar.gz

# Test results
test-results/
junit.xml

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Vitest
coverage/

# Local configuration files
config/local.json
config/development.json
config/production.json

# Backup files
*.backup
*.bak
*.orig

# Lock files (choose one based on your package manager)
# npm
package-lock.json
# yarn
# yarn.lock
# pnpm
# pnpm-lock.yaml

# Auto-generated files
src/shared/types/generated/

# Application specific
user-data/
app-data/
settings.json
config.json
*.sqlite
*.db

# Screenshots and recordings
screenshots/
recordings/
*.png
*.jpg
*.jpeg
*.gif
*.mp4
*.webm

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Documentation build
docs/build/
docs/.vuepress/dist/