{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking"], "ignorePatterns": ["dist", "build", "release", "node_modules", ".eslintrc.json", "vite.config.ts", "tailwind.config.js", "postcss.config.js"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": ["./tsconfig.json", "./tsconfig.main.json"], "tsconfigRootDir": "."}, "plugins": ["react-refresh", "@typescript-eslint"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/restrict-template-expressions": "warn", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/require-await": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-template": "error"}, "overrides": [{"files": ["src/renderer/**/*"], "env": {"browser": true, "node": false}, "extends": ["plugin:react-hooks/recommended"], "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}}, {"files": ["src/main/**/*", "src/preload/**/*"], "env": {"browser": false, "node": true}, "rules": {"no-console": "off"}}, {"files": ["tests/**/*", "**/*.test.ts", "**/*.spec.ts"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off"}}]}