{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@components/*": ["src/renderer/components/*"], "@pages/*": ["src/renderer/pages/*"], "@hooks/*": ["src/renderer/hooks/*"], "@stores/*": ["src/renderer/stores/*"], "@utils/*": ["src/utils/*"], "@assets/*": ["assets/*"]}, "types": ["node", "jest", "vite/client"]}, "include": ["src/renderer/**/*", "src/shared/**/*", "src/utils/**/*", "vite.config.ts"], "exclude": ["node_modules", "src/main", "src/services", "tests", "dist", "build", "out", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}