{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@components/*": ["src/renderer/components/*"], "@pages/*": ["src/renderer/pages/*"], "@hooks/*": ["src/renderer/hooks/*"], "@stores/*": ["src/renderer/stores/*"], "@assets/*": ["assets/*"]}, "types": ["node", "jest", "electron"]}, "include": ["src/**/*", "tests/**/*", "*.d.ts"], "exclude": ["node_modules", "dist", "build", "out", "dist-electron", "**/*.js"], "references": [{"path": "./tsconfig.main.json"}, {"path": "./tsconfig.renderer.json"}]}