# Desktop AI Assistant

> 智能桌面AI助手 - 不打扰、隐身、谁叫谁出的设计理念

## 🌟 项目概述

Desktop AI Assistant 是一款基于 Electron 的智能桌面助手应用，采用"不打扰、隐身、谁叫谁出"的设计理念。它能够通过语音激活和快捷键触发，智能识别桌面内容，并与医疗集成系统进行交互，为用户提供智能建议和任务执行功能。

### 核心特性

- 🎤 **语音激活控制** - 支持语音唤醒和语音指令
- ⌨️ **快捷键触发** - 自定义全局快捷键快速调用
- 👁️ **桌面内容识别** - OCR文字识别和可访问性API集成
- 🏥 **医疗系统集成** - 与medical-integration-system无缝对接
- 🤖 **智能建议引擎** - 基于上下文的智能推荐
- 🔒 **本地优先隐私** - 数据本地处理，保护用户隐私
- 🎨 **现代化界面** - 支持深色模式的美观UI
- 🚀 **高性能架构** - TypeScript + React + Electron技术栈

## 🏗️ 技术架构

### 技术栈

- **前端框架**: React 18 + TypeScript
- **桌面框架**: Electron
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **状态管理**: Zustand
- **测试框架**: Jest + Playwright
- **代码质量**: ESLint + Prettier

### 项目结构

```
desktop-ai-assistant/
├── src/                    # 源代码目录
│   ├── main/              # Electron主进程
│   │   ├── index.ts       # 主进程入口
│   │   ├── preload.ts     # 预加载脚本
│   │   └── windows/       # 窗口管理
│   ├── renderer/          # 渲染进程(React)
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── stores/        # 状态管理
│   │   └── utils/         # 工具函数
│   ├── services/          # 业务服务
│   │   ├── ai/           # AI服务
│   │   ├── voice/        # 语音服务
│   │   ├── desktop/      # 桌面识别
│   │   └── medical/      # 医疗集成
│   ├── shared/           # 共享类型和工具
│   └── utils/            # 通用工具
├── docs/                 # 项目文档
├── tests/                # 测试文件
├── config/               # 配置文件
├── scripts/              # 构建脚本
├── assets/               # 静态资源
└── dist/                 # 构建输出
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0 或 pnpm >= 7.0.0
- 操作系统: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 pnpm (推荐)
pnpm install
```

### 开发模式

```bash
# 启动开发服务器
npm run dev

# 或
pnpm dev
```

### 构建应用

```bash
# 构建生产版本
npm run build

# 打包应用程序
npm run dist

# 构建并打包
npm run build:all
```

### 测试

```bash
# 运行单元测试
npm run test

# 运行E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

## 📖 使用指南

### 基本操作

1. **启动应用**: 双击桌面图标或从开始菜单启动
2. **语音激活**: 说出唤醒词"小助手"或按下设定的快捷键
3. **桌面识别**: 应用会自动识别当前屏幕内容
4. **智能交互**: 根据识别结果提供相关建议和操作

### 快捷键

- `Ctrl/Cmd + Shift + A`: 显示/隐藏主窗口
- `Ctrl/Cmd + Shift + V`: 显示/隐藏语音窗口
- `Ctrl/Cmd + Shift + F`: 显示/隐藏浮动窗口
- `Esc`: 隐藏当前窗口
- `Space`: 开始/停止语音识别(在语音窗口中)

### 配置选项

应用支持丰富的配置选项，可通过设置界面或配置文件进行调整：

- 语音识别语言和敏感度
- 快捷键自定义
- 主题和外观设置
- 隐私和安全选项
- 医疗系统连接配置

## 🔧 开发指南

### 代码规范

项目使用 ESLint 和 Prettier 确保代码质量：

```bash
# 检查代码规范
npm run lint

# 自动修复
npm run lint:fix

# 格式化代码
npm run format
```

### 提交规范

使用 Conventional Commits 规范：

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 调试

```bash
# 启动调试模式
npm run dev:debug

# 主进程调试
npm run debug:main

# 渲染进程调试
npm run debug:renderer
```

## 🔌 API集成

### Medical Integration System

应用与 medical-integration-system 进行深度集成：

```typescript
// 患者搜索
const patients = await medicalAPI.searchPatients({
  query: '张三',
  limit: 10
});

// 获取患者详情
const patient = await medicalAPI.getPatient(patientId);

// 创建预约
const appointment = await medicalAPI.createAppointment({
  patientId,
  doctorId,
  datetime: new Date(),
  type: 'consultation'
});
```

### AI服务

```typescript
// 处理用户消息
const response = await aiService.processMessage({
  message: '帮我查找张三的病历',
  context: {
    screenContent: ocrResult,
    currentApp: 'medical-system'
  }
});

// 生成摘要
const summary = await aiService.generateSummary({
  content: patientRecord,
  type: 'medical-record'
});
```

## 🛡️ 安全与隐私

### 数据保护

- **本地优先**: 敏感数据优先在本地处理
- **加密存储**: 配置和缓存数据使用AES加密
- **权限控制**: 严格的API访问权限管理
- **审计日志**: 完整的操作日志记录

### 隐私设置

- 可选择性数据收集
- 用户完全控制数据共享范围
- 支持数据导出和删除
- 透明的隐私政策

## 📊 性能优化

### 启动优化

- 延迟加载非核心模块
- 预编译常用组件
- 智能缓存策略

### 内存管理

- 自动垃圾回收
- 图片和媒体资源优化
- 进程间通信优化

### 响应性能

- 虚拟滚动长列表
- 防抖和节流优化
- Web Workers处理重计算

## 🚀 部署

### 自动构建

项目支持多平台自动构建：

```bash
# Windows
npm run build:win

# macOS
npm run build:mac

# Linux
npm run build:linux

# 全平台
npm run build:all
```

### 发布流程

1. 更新版本号
2. 运行完整测试套件
3. 构建所有平台版本
4. 生成发布说明
5. 创建GitHub Release
6. 自动分发更新

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/your-username/desktop-ai-assistant.git
cd desktop-ai-assistant

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm test
```

## 📝 更新日志

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的版本更新信息。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](./LICENSE) 文件了解详情。

## 🙏 致谢

- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [React](https://reactjs.org/) - 用户界面库
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [Medical Integration System](../medical-integration-system) - 医疗系统集成

## 📞 支持

如果您遇到问题或有建议，请通过以下方式联系我们：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/desktop-ai-assistant/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-username/desktop-ai-assistant/discussions)
- 📖 文档: [在线文档](https://docs.desktop-ai-assistant.com)

---

**Desktop AI Assistant** - 让AI助手真正融入您的工作流程 🚀