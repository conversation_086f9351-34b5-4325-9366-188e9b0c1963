<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="50%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#60A5FA"/>
    </linearGradient>
    
    <radialGradient id="centerGlow" cx="50%" cy="50%" r="40%">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0"/>
    </radialGradient>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bgGradient)"/>
  <rect width="400" height="300" fill="url(#centerGlow)"/>
  
  <!-- Main logo circle -->
  <g transform="translate(200, 120)">
    <!-- Outer glow circle -->
    <circle cx="0" cy="0" r="50" fill="#FFFFFF" opacity="0.1" filter="url(#glow)"/>
    
    <!-- Main circle -->
    <circle cx="0" cy="0" r="40" fill="#FFFFFF" opacity="0.95" stroke="#E5E7EB" stroke-width="1"/>
    
    <!-- AI Brain/Circuit pattern -->
    <g>
      <!-- Central core -->
      <circle cx="0" cy="0" r="12" fill="#3B82F6" opacity="0.9"/>
      
      <!-- Neural network connections -->
      <g stroke="#3B82F6" stroke-width="1" opacity="0.6">
        <!-- Horizontal lines -->
        <line x1="-25" y1="-16" x2="25" y2="-16"/>
        <line x1="-25" y1="0" x2="-12" y2="0"/>
        <line x1="12" y1="0" x2="25" y2="0"/>
        <line x1="-25" y1="16" x2="25" y2="16"/>
        
        <!-- Vertical lines -->
        <line x1="-16" y1="-25" x2="-16" y2="25"/>
        <line x1="0" y1="-25" x2="0" y2="-12"/>
        <line x1="0" y1="12" x2="0" y2="25"/>
        <line x1="16" y1="-25" x2="16" y2="25"/>
        
        <!-- Diagonal connections -->
        <line x1="-16" y1="-16" x2="-8" y2="-8"/>
        <line x1="16" y1="-16" x2="8" y2="-8"/>
        <line x1="-16" y1="16" x2="-8" y2="8"/>
        <line x1="16" y1="16" x2="8" y2="8"/>
      </g>
      
      <!-- Neural nodes -->
      <g fill="#3B82F6">
        <circle cx="-16" cy="-16" r="2"/>
        <circle cx="16" cy="-16" r="2"/>
        <circle cx="-16" cy="16" r="2"/>
        <circle cx="16" cy="16" r="2"/>
        <circle cx="-25" cy="0" r="1.5"/>
        <circle cx="25" cy="0" r="1.5"/>
        <circle cx="0" cy="-25" r="1.5"/>
        <circle cx="0" cy="25" r="1.5"/>
      </g>
      
      <!-- Central AI symbol -->
      <text x="0" y="3" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="8" font-weight="bold">AI</text>
    </g>
  </g>
  
  <!-- App title -->
  <g transform="translate(200, 200)">
    <text x="0" y="0" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="24" font-weight="bold">桌面AI助手</text>
    <text x="0" y="25" text-anchor="middle" fill="#E5E7EB" font-family="Arial, sans-serif" font-size="14">Desktop AI Assistant</text>
  </g>
  
  <!-- Subtitle -->
  <g transform="translate(200, 250)">
    <text x="0" y="0" text-anchor="middle" fill="#D1D5DB" font-family="Arial, sans-serif" font-size="12">智能识别 · 语音交互 · 医疗集成</text>
  </g>
  
  <!-- Loading animation dots -->
  <g transform="translate(200, 270)">
    <circle cx="-12" cy="0" r="2" fill="#FFFFFF" opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="0" cy="0" r="2" fill="#FFFFFF" opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="12" cy="0" r="2" fill="#FFFFFF" opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite" begin="1s"/>
    </circle>
  </g>
  
  <!-- Floating particles -->
  <g opacity="0.3">
    <circle cx="50" cy="50" r="1" fill="#FFFFFF">
      <animateTransform attributeName="transform" type="translate" values="0,0; 10,-10; 0,0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="350" cy="80" r="1.5" fill="#FFFFFF">
      <animateTransform attributeName="transform" type="translate" values="0,0; -15,5; 0,0" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="220" r="1" fill="#FFFFFF">
      <animateTransform attributeName="transform" type="translate" values="0,0; 5,15; 0,0" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="320" cy="200" r="1.5" fill="#FFFFFF">
      <animateTransform attributeName="transform" type="translate" values="0,0; -8,-12; 0,0" dur="2.8s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>