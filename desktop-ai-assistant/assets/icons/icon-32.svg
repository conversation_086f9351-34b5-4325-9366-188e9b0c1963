<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="#3B82F6" stroke="#1E40AF" stroke-width="0.5"/>
  
  <!-- AI Brain/Circuit pattern -->
  <g transform="translate(8, 8)">
    <!-- Central core -->
    <circle cx="8" cy="8" r="3" fill="#FFFFFF" opacity="0.9"/>
    
    <!-- Neural network connections -->
    <g stroke="#FFFFFF" stroke-width="0.25" opacity="0.7">
      <!-- Horizontal lines -->
      <line x1="2.5" y1="4" x2="13.5" y2="4"/>
      <line x1="2.5" y1="8" x2="5" y2="8"/>
      <line x1="11" y1="8" x2="13.5" y2="8"/>
      <line x1="2.5" y1="12" x2="13.5" y2="12"/>
      
      <!-- Vertical lines -->
      <line x1="4" y1="2.5" x2="4" y2="13.5"/>
      <line x1="8" y1="2.5" x2="8" y2="5"/>
      <line x1="8" y1="11" x2="8" y2="13.5"/>
      <line x1="12" y1="2.5" x2="12" y2="13.5"/>
      
      <!-- Diagonal connections -->
      <line x1="4" y1="4" x2="7" y2="7"/>
      <line x1="12" y1="4" x2="9" y2="7"/>
      <line x1="4" y1="12" x2="7" y2="9"/>
      <line x1="12" y1="12" x2="9" y2="9"/>
    </g>
    
    <!-- Neural nodes -->
    <g fill="#FFFFFF">
      <circle cx="4" cy="4" r="0.5"/>
      <circle cx="12" cy="4" r="0.5"/>
      <circle cx="4" cy="12" r="0.5"/>
      <circle cx="12" cy="12" r="0.5"/>
      <circle cx="2.5" cy="8" r="0.4"/>
      <circle cx="13.5" cy="8" r="0.4"/>
      <circle cx="8" cy="2.5" r="0.4"/>
      <circle cx="8" cy="13.5" r="0.4"/>
    </g>
    
    <!-- Central AI symbol -->
    <g transform="translate(8, 8)">
      <!-- AI text -->
      <text x="0" y="1" text-anchor="middle" fill="#3B82F6" font-family="Arial, sans-serif" font-size="2" font-weight="bold">AI</text>
    </g>
  </g>
  
  <!-- Desktop/Monitor outline -->
  <g transform="translate(5, 22.5)">
    <!-- Monitor screen -->
    <rect x="0" y="0" width="22" height="14" rx="1" fill="none" stroke="#FFFFFF" stroke-width="0.4" opacity="0.8"/>
    
    <!-- Monitor stand -->
    <rect x="9.75" y="14" width="2.5" height="2.5" fill="#FFFFFF" opacity="0.8"/>
    <rect x="7.5" y="16.5" width="7" height="1" rx="0.5" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Screen content (simplified desktop) -->
    <g opacity="0.6">
      <!-- Window -->
      <rect x="2.5" y="2.5" width="17" height="9" rx="0.5" fill="#FFFFFF" opacity="0.3"/>
      <!-- Title bar -->
      <rect x="2.5" y="2.5" width="17" height="2" rx="0.5" fill="#FFFFFF" opacity="0.5"/>
      <!-- Window controls -->
      <circle cx="3.75" cy="3.5" r="0.25" fill="#EF4444"/>
      <circle cx="5.25" cy="3.5" r="0.25" fill="#F59E0B"/>
      <circle cx="6.75" cy="3.5" r="0.25" fill="#10B981"/>
    </g>
  </g>
  
  <!-- Subtle glow effect -->
  <defs>
    <radialGradient id="glow32" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <circle cx="16" cy="16" r="16" fill="url(#glow32)"/>
</svg>