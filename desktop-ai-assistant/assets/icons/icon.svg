<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="#3B82F6" stroke="#1E40AF" stroke-width="4"/>
  
  <!-- AI Brain/Circuit pattern -->
  <g transform="translate(64, 64)">
    <!-- Central core -->
    <circle cx="64" cy="64" r="24" fill="#FFFFFF" opacity="0.9"/>
    
    <!-- Neural network connections -->
    <g stroke="#FFFFFF" stroke-width="2" opacity="0.7">
      <!-- Horizontal lines -->
      <line x1="20" y1="32" x2="108" y2="32"/>
      <line x1="20" y1="64" x2="40" y2="64"/>
      <line x1="88" y1="64" x2="108" y2="64"/>
      <line x1="20" y1="96" x2="108" y2="96"/>
      
      <!-- Vertical lines -->
      <line x1="32" y1="20" x2="32" y2="108"/>
      <line x1="64" y1="20" x2="64" y2="40"/>
      <line x1="64" y1="88" x2="64" y2="108"/>
      <line x1="96" y1="20" x2="96" y2="108"/>
      
      <!-- Diagonal connections -->
      <line x1="32" y1="32" x2="56" y2="56"/>
      <line x1="96" y1="32" x2="72" y2="56"/>
      <line x1="32" y1="96" x2="56" y2="72"/>
      <line x1="96" y1="96" x2="72" y2="72"/>
    </g>
    
    <!-- Neural nodes -->
    <g fill="#FFFFFF">
      <circle cx="32" cy="32" r="4"/>
      <circle cx="96" cy="32" r="4"/>
      <circle cx="32" cy="96" r="4"/>
      <circle cx="96" cy="96" r="4"/>
      <circle cx="20" cy="64" r="3"/>
      <circle cx="108" cy="64" r="3"/>
      <circle cx="64" cy="20" r="3"/>
      <circle cx="64" cy="108" r="3"/>
    </g>
    
    <!-- Central AI symbol -->
    <g transform="translate(64, 64)">
      <!-- AI text -->
      <text x="0" y="6" text-anchor="middle" fill="#3B82F6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">AI</text>
    </g>
  </g>
  
  <!-- Desktop/Monitor outline -->
  <g transform="translate(40, 180)">
    <!-- Monitor screen -->
    <rect x="0" y="0" width="176" height="110" rx="8" fill="none" stroke="#FFFFFF" stroke-width="3" opacity="0.8"/>
    
    <!-- Monitor stand -->
    <rect x="78" y="110" width="20" height="20" fill="#FFFFFF" opacity="0.8"/>
    <rect x="60" y="130" width="56" height="8" rx="4" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Screen content (simplified desktop) -->
    <g opacity="0.6">
      <!-- Window -->
      <rect x="20" y="20" width="136" height="70" rx="4" fill="#FFFFFF" opacity="0.3"/>
      <!-- Title bar -->
      <rect x="20" y="20" width="136" height="16" rx="4" fill="#FFFFFF" opacity="0.5"/>
      <!-- Window controls -->
      <circle cx="30" cy="28" r="2" fill="#EF4444"/>
      <circle cx="42" cy="28" r="2" fill="#F59E0B"/>
      <circle cx="54" cy="28" r="2" fill="#10B981"/>
    </g>
  </g>
  
  <!-- Subtle glow effect -->
  <defs>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <circle cx="128" cy="128" r="128" fill="url(#glow)"/>
</svg>