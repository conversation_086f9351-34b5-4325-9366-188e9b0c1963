<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E5E7EB;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Main background -->
  <circle cx="16" cy="16" r="15" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- AI Brain icon -->
  <g transform="translate(8, 8)">
    <!-- Brain outline -->
    <path d="M8 2C6.5 2 5.2 2.8 4.5 4C3.8 3.2 2.8 2.7 1.7 2.7C0.8 2.7 0 3.5 0 4.4C0 5.3 0.8 6.1 1.7 6.1C2.1 6.1 2.4 6 2.7 5.8C2.6 6.2 2.5 6.6 2.5 7C2.5 8.9 4 10.5 6 10.5C6.3 10.5 6.6 10.4 6.9 10.3C7.2 11.3 8.1 12 9.1 12C10.4 12 11.5 10.9 11.5 9.6C11.5 9.2 11.4 8.8 11.2 8.5C12.3 7.8 13 6.5 13 5C13 3.3 11.7 2 10 2C9.3 2 8.6 2.3 8 2Z" fill="url(#iconGradient)" opacity="0.9"/>
    
    <!-- Neural connections -->
    <circle cx="4" cy="5" r="0.8" fill="#3B82F6" opacity="0.8"/>
    <circle cx="8" cy="4" r="0.8" fill="#3B82F6" opacity="0.8"/>
    <circle cx="10" cy="6" r="0.8" fill="#3B82F6" opacity="0.8"/>
    <circle cx="6" cy="8" r="0.8" fill="#3B82F6" opacity="0.8"/>
    
    <!-- Connection lines -->
    <line x1="4" y1="5" x2="8" y2="4" stroke="#3B82F6" stroke-width="0.5" opacity="0.6"/>
    <line x1="8" y1="4" x2="10" y2="6" stroke="#3B82F6" stroke-width="0.5" opacity="0.6"/>
    <line x1="4" y1="5" x2="6" y2="8" stroke="#3B82F6" stroke-width="0.5" opacity="0.6"/>
    <line x1="10" y1="6" x2="6" y2="8" stroke="#3B82F6" stroke-width="0.5" opacity="0.6"/>
  </g>
  
  <!-- Voice wave indicator -->
  <g transform="translate(20, 20)" opacity="0.7">
    <circle cx="0" cy="0" r="1" fill="#10B981">
      <animate attributeName="r" values="1;2;1" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="2" fill="none" stroke="#10B981" stroke-width="0.5">
      <animate attributeName="r" values="2;3;2" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.1;0.5" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Medical cross (small) -->
  <g transform="translate(24, 8)" opacity="0.6">
    <rect x="-1" y="-3" width="2" height="6" fill="#EF4444" rx="1"/>
    <rect x="-3" y="-1" width="6" height="2" fill="#EF4444" rx="1"/>
  </g>
</svg>