<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Desktop AI Assistant - 语音交互窗口" />
    
    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self' data:;" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/icons/favicon.svg" />
    
    <!-- Fonts: 使用系统字体栈，移除外网字体依赖 -->
    
    <!-- Theme -->
    <script>
      // 防止闪烁的主题初始化
      (function() {
        const theme = localStorage.getItem('theme') || 'system';
        if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
    
    <title>AI Assistant - 语音交互</title>
  </head>
  <body class="bg-transparent font-sans antialiased overflow-hidden">
    <!-- 语音窗口根节点 -->
    <div id="voice-root" class="w-full h-full"></div>
    
    <!-- 加载指示器 -->
    <div id="voice-loading" class="fixed inset-0 flex items-center justify-center">
      <div class="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-full p-6 shadow-lg">
        <div class="flex flex-col items-center space-y-3">
          <div class="relative">
            <div class="animate-pulse rounded-full h-12 w-12 bg-primary-600/20"></div>
            <div class="absolute inset-0 animate-ping rounded-full h-12 w-12 bg-primary-600/40"></div>
            <div class="absolute inset-2 rounded-full bg-primary-600"></div>
          </div>
          <span class="text-sm text-gray-600 dark:text-gray-400">初始化语音...</span>
        </div>
      </div>
    </div>
    
    <!-- 错误边界 -->
    <div id="voice-error" class="hidden fixed inset-0 flex items-center justify-center">
      <div class="bg-red-50/90 dark:bg-red-900/20 backdrop-blur-sm rounded-lg p-6 shadow-lg max-w-sm">
        <div class="flex items-center space-x-3 mb-4">
          <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span class="text-lg font-medium text-red-800 dark:text-red-200">语音错误</span>
        </div>
        <p id="voice-error-message" class="text-sm text-red-700 dark:text-red-300 mb-4">语音功能遇到错误</p>
        <div class="flex space-x-3">
          <button
            id="voice-retry"
            class="flex-1 px-4 py-2 text-sm bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            重试
          </button>
          <button
            id="voice-close"
            class="flex-1 px-4 py-2 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
    
    <!-- 权限请求 -->
    <div id="voice-permission" class="hidden fixed inset-0 flex items-center justify-center">
      <div class="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg p-6 shadow-lg max-w-sm">
        <div class="flex items-center space-x-3 mb-4">
          <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
          <span class="text-lg font-medium text-gray-900 dark:text-gray-100">麦克风权限</span>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          AI助手需要访问您的麦克风来进行语音识别。请点击允许以启用语音功能。
        </p>
        <div class="flex space-x-3">
          <button
            id="voice-allow"
            class="flex-1 px-4 py-2 text-sm bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            允许
          </button>
          <button
            id="voice-deny"
            class="flex-1 px-4 py-2 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            拒绝
          </button>
        </div>
      </div>
    </div>
    
    <!-- 脚本 -->
    <script type="module" src="/src/renderer/voice.tsx"></script>
    
    <!-- 错误处理和工具脚本 -->
    <script>
      // 全局错误处理
      window.addEventListener('error', function(event) {
        console.error('Voice window error:', event.error);
        showVoiceError(event.error?.message || '未知错误');
      });
      
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Voice window unhandled rejection:', event.reason);
        showVoiceError(event.reason?.message || '未处理的Promise拒绝');
      });
      
      function showVoiceError(message) {
        const loading = document.getElementById('voice-loading');
        const permission = document.getElementById('voice-permission');
        const error = document.getElementById('voice-error');
        const errorMessage = document.getElementById('voice-error-message');
        
        if (loading) loading.style.display = 'none';
        if (permission) permission.classList.add('hidden');
        if (error) error.classList.remove('hidden');
        if (errorMessage) errorMessage.textContent = message;
      }
      
      function showVoicePermission() {
        const loading = document.getElementById('voice-loading');
        const permission = document.getElementById('voice-permission');
        const error = document.getElementById('voice-error');
        
        if (loading) loading.style.display = 'none';
        if (error) error.classList.add('hidden');
        if (permission) permission.classList.remove('hidden');
      }
      
      function hideAllModals() {
        const loading = document.getElementById('voice-loading');
        const permission = document.getElementById('voice-permission');
        const error = document.getElementById('voice-error');
        
        if (loading) loading.style.display = 'none';
        if (permission) permission.classList.add('hidden');
        if (error) error.classList.add('hidden');
      }
      
      // 按钮事件处理
      document.getElementById('voice-retry')?.addEventListener('click', function() {
        window.location.reload();
      });
      
      document.getElementById('voice-close')?.addEventListener('click', function() {
        if (window.electronAPI?.window?.hideVoice) {
          window.electronAPI.window.hideVoice();
        }
      });
      
      document.getElementById('voice-allow')?.addEventListener('click', function() {
        requestMicrophonePermission();
      });
      
      document.getElementById('voice-deny')?.addEventListener('click', function() {
        if (window.electronAPI?.window?.hideVoice) {
          window.electronAPI.window.hideVoice();
        }
      });
      
      // 麦克风权限请求
      async function requestMicrophonePermission() {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          // 立即停止流，我们只是检查权限
          stream.getTracks().forEach(track => track.stop());
          hideAllModals();
          // 通知主进程权限已获得
          if (window.electronAPI?.voice?.onPermissionGranted) {
            window.electronAPI.voice.onPermissionGranted();
          }
        } catch (error) {
          console.error('Microphone permission denied:', error);
          showVoiceError('麦克风权限被拒绝，语音功能无法使用');
        }
      }
      
      // 检查麦克风权限
      async function checkMicrophonePermission() {
        try {
          const result = await navigator.permissions.query({ name: 'microphone' });
          if (result.state === 'granted') {
            hideAllModals();
          } else if (result.state === 'prompt') {
            showVoicePermission();
          } else {
            showVoiceError('麦克风权限被拒绝');
          }
        } catch (error) {
          // 某些浏览器不支持 permissions API，直接尝试请求
          showVoicePermission();
        }
      }
      
      // 隐藏加载指示器
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const loading = document.getElementById('voice-loading');
          const root = document.getElementById('voice-root');
          if (loading && root && root.children.length > 0) {
            loading.style.display = 'none';
          } else {
            // 如果React组件还没加载，检查麦克风权限
            checkMicrophonePermission();
          }
        }, 500);
      });
      
      // 键盘快捷键
      document.addEventListener('keydown', function(e) {
        // ESC 键隐藏语音窗口
        if (e.key === 'Escape' && window.electronAPI?.window?.hideVoice) {
          window.electronAPI.window.hideVoice();
        }
        
        // 空格键开始/停止录音（当没有输入框焦点时）
        if (e.code === 'Space' && !e.target.matches('input, textarea, [contenteditable]')) {
          e.preventDefault();
          if (window.electronAPI?.voice?.toggleRecognition) {
            window.electronAPI.voice.toggleRecognition();
          }
        }
        
        // Ctrl/Cmd + Enter 发送消息
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
          const event = new CustomEvent('voice-send-message');
          document.dispatchEvent(event);
        }
      });
      
      // 窗口焦点管理
      window.addEventListener('focus', function() {
        // 获得焦点时检查语音状态
        if (window.electronAPI?.voice?.getState) {
          window.electronAPI.voice.getState().then(function(state) {
            if (state.isListening) {
              // 如果正在监听，确保UI状态正确
              const event = new CustomEvent('voice-state-update', { detail: state });
              document.dispatchEvent(event);
            }
          }).catch(function(error) {
            console.error('Failed to get voice state:', error);
          });
        }
      });
      
      // 防止右键菜单（在生产环境中）
      if (process?.env?.NODE_ENV === 'production') {
        document.addEventListener('contextmenu', function(e) {
          e.preventDefault();
        });
      }
      
      // 语音可视化支持
      let audioContext;
      let analyser;
      let microphone;
      let dataArray;
      
      window.startVoiceVisualization = async function() {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          audioContext = new (window.AudioContext || window.webkitAudioContext)();
          analyser = audioContext.createAnalyser();
          microphone = audioContext.createMediaStreamSource(stream);
          
          analyser.fftSize = 256;
          const bufferLength = analyser.frequencyBinCount;
          dataArray = new Uint8Array(bufferLength);
          
          microphone.connect(analyser);
          
          return { analyser, dataArray, bufferLength };
        } catch (error) {
          console.error('Failed to start voice visualization:', error);
          throw error;
        }
      };
      
      window.stopVoiceVisualization = function() {
        if (microphone) {
          microphone.disconnect();
          microphone = null;
        }
        if (audioContext) {
          audioContext.close();
          audioContext = null;
        }
        analyser = null;
        dataArray = null;
      };
    </script>
  </body>
</html>