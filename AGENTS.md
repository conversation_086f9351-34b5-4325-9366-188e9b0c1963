# Repository Guidelines

## Project Structure & Module Organization
- `medical-integration-system/` (primary app): Vite + React + TypeScript. Source in `src/` (components, pages, utils, store, types). Static assets in `public/` (sample DICOM under `public/dicom`). Build output in `dist/`.
- `demo_RIS/`, `demo.1/`: earlier demos (Vite + React). Useful for reference; not the primary target.
- `.vercel/`, `vercel.json`: deployment configuration.

## Build, Test, and Development Commands
- Primary app
  - Install: `cd medical-integration-system && pnpm i`
  - Dev server: `pnpm dev` (serves on `http://localhost:9527`)
  - Build: `pnpm build` (outputs to `dist/`)
  - Preview build: `pnpm preview`
  - Lint: `pnpm lint`  • Type-check: `pnpm check`
- Demos
  - `cd demo_RIS && npm ci && npm run dev`
  - `cd demo.1 && npm ci && npm run dev`

## Coding Style & Naming Conventions
- Language: TypeScript. Prefer explicit types for public APIs.
- Indentation: 2 spaces. Line width: follow editor defaults; keep imports tidy.
- React components: PascalCase (`PatientList.tsx`). Files: camelCase (`dicomUtils.ts`).
- Imports: prefer `@/` alias to `src/` (see `medical-integration-system/tsconfig.json`).
- Linting: ESLint configured (hooks + refresh). Run `pnpm lint` before PRs.
- Styling: Tailwind CSS; avoid inline styles unless necessary.

## Testing Guidelines
- No unit test runner is configured yet. For now, rely on `pnpm check` and `pnpm lint`.
- If adding tests, use Vitest with co-located files named `*.test.ts(x)` under `src/`. Example script: add `"test": "vitest"` to `package.json` and run `pnpm test`.

## Commit & Pull Request Guidelines
- Commits: use Conventional Commits.
  - Types: `feat`, `fix`, `docs`, `style`, `refactor`, `chore`.
  - Example: `feat(radiology): add series sorting by instanceNumber`.
- PRs: include scope (`medical-integration-system` or demo), summary, screenshots for UI changes, linked issues, and local verification notes (build, dev run, lint).

## Security & Configuration Tips
- Do not commit PHI. Keep sample DICOM anonymized in `medical-integration-system/public/dicom`.
- Dev server: port set in `medical-integration-system/vite.config.ts:8` (`9527`). Avoid relaxing `server.fs.allow` beyond current values.
- Environment: store secrets only in untracked `.env.local` and access via `import.meta.env`.
