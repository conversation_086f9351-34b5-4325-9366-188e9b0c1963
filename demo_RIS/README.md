# 医疗影像报告系统 (Medical Imaging Report System)

这是一个专为影像科医生设计的医疗报告编写平台，医生可以通过查看医学影像来编写和管理患者的诊断报告。

## 功能特性

### 🏥 核心功能
- **患者管理**: 左侧患者列表，支持搜索和筛选
- **影像查看**: 中央影像显示区域，支持缩放、平移、窗宽窗位调节
- **报告编辑**: 右侧报告编辑面板，包含结构化模板
- **模板系统**: 多种检查类型的报告模板
- **保存打印**: 报告保存、打印和导出功能

### 🎨 界面特点
- **三栏布局**: 患者列表 + 影像查看 + 报告编辑
- **响应式设计**: 适配大屏幕医疗工作站
- **医疗主题**: 专业的医疗界面设计
- **实时保存**: 自动检测内容变更

### 📋 模拟数据
系统包含以下模拟患者和报告：
1. **张三** - 胸部CT检查
2. **李四** - 腹部MRI检查
3. **王五** - 头颅CT检查
4. **赵六** - 膝关节MRI检查

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **状态管理**: Zustand
- **图标库**: Lucide React
- **通知组件**: Sonner
- **路由**: React Router

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 使用说明

### 1. 选择患者
- 在左侧患者列表中点击选择患者
- 可以使用搜索框按姓名或检查类型筛选
- 选中的患者会高亮显示

### 2. 查看影像
- 中央区域会自动加载选中患者的影像
- 支持鼠标滚轮缩放
- 支持拖拽移动影像
- 可调节窗宽窗位、亮度对比度

### 3. 编写报告
- 右侧面板提供报告编辑功能
- 可选择对应的报告模板
- 包含检查所见、诊断意见、建议三个部分
- 支持预览模式查看最终效果

### 4. 保存和打印
- 点击保存按钮保存报告
- 支持打印功能生成标准医疗报告
- 可导出JSON格式的报告数据

## 项目结构

```
src/
├── components/          # 组件目录
│   ├── PatientPanel.tsx    # 患者选择面板
│   ├── ImageViewer.tsx     # 影像查看器
│   ├── ReportPanel.tsx     # 报告编辑面板
│   └── TopToolbar.tsx      # 顶部工具栏
├── data/               # 数据目录
│   └── mockData.ts        # 模拟数据
├── pages/              # 页面目录
│   └── Home.tsx           # 主页面
├── store/              # 状态管理
│   └── useReportStore.ts  # 报告状态管理
├── types/              # 类型定义
│   └── index.ts           # 类型定义文件
└── App.tsx             # 应用入口
```

## 开发说明

### 添加新的检查类型
1. 在 `mockData.ts` 中添加新的模板
2. 添加对应的患者数据
3. 更新类型定义（如需要）

### 自定义样式
- 使用 Tailwind CSS 进行样式定制
- 主色调：医疗蓝色 `#2E86AB`
- 遵循医疗界面设计规范

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
