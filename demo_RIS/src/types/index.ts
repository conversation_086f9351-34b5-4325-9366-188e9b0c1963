// 患者信息类型定义
export interface Patient {
  id: string;
  name: string;
  age: number;
  gender: '男' | '女';
  examDate: string;
  examType: string;
  studyId: string;
  chiefComplaint: string; // 主诉
  avatar?: string;
}

// 医学影像类型定义
export interface MedicalImage {
  id: string;
  patientId: string;
  seriesName: string;
  imageUrl: string;
  thumbnailUrl: string;
  sequenceNumber: number;
}

// 报告类型定义
export interface MedicalReport {
  id: string;
  patientId: string;
  findings: string; // 检查所见
  diagnosis: string; // 诊断意见
  suggestion: string; // 建议
  template: string; // 使用的模板
  criticalValue: '上报' | '不上报'; // 危机值：上报/不上报
  hpTest: '阳性' | '阴性'; // HP检测：阳性/阴性
  createdAt: string;
  updatedAt: string;
  status: 'draft' | 'completed' | 'reviewed';
}

// 报告模板类型定义
export interface ReportTemplate {
  id: string;
  name: string;
  examType: string;
  findingsTemplate: string;
  diagnosisTemplate: string;
  suggestionTemplate: string;
}

// 影像查看器设置
export interface ViewerSettings {
  zoom: number;
  windowWidth: number;
  windowCenter: number;
  brightness: number;
  contrast: number;
}