import { create } from 'zustand';
import { Patient, MedicalReport, ReportTemplate, ViewerSettings } from '../types';
import { mockPatients, mockReports, mockTemplates } from '../data/mockData';

interface ReportStore {
  // 患者相关
  patients: Patient[];
  selectedPatient: Patient | null;
  setSelectedPatient: (patient: Patient) => void;
  
  // 报告相关
  reports: MedicalReport[];
  currentReport: MedicalReport | null;
  setCurrentReport: (report: MedicalReport) => void;
  updateReport: (reportId: string, updates: Partial<MedicalReport>) => void;
  saveReport: () => void;
  
  // 模板相关
  templates: ReportTemplate[];
  selectedTemplate: ReportTemplate | null;
  setSelectedTemplate: (template: ReportTemplate) => void;
  applyTemplate: () => void;
  
  // 影像查看器设置
  viewerSettings: ViewerSettings;
  updateViewerSettings: (settings: Partial<ViewerSettings>) => void;
  
  // UI状态
  isReportModified: boolean;
  setReportModified: (modified: boolean) => void;
}

export const useReportStore = create<ReportStore>((set, get) => ({
  // 初始化数据
  patients: mockPatients,
  selectedPatient: null,
  reports: mockReports,
  currentReport: null,
  templates: mockTemplates,
  selectedTemplate: null,
  
  // 初始化影像查看器设置
  viewerSettings: {
    zoom: 1.0,
    windowWidth: 400,
    windowCenter: 40,
    brightness: 50,
    contrast: 50,
  },
  
  isReportModified: false,
  
  // 患者选择
  setSelectedPatient: (patient: Patient) => {
    const reports = get().reports;
    const patientReport = reports.find(r => r.patientId === patient.id);
    
    set({
      selectedPatient: patient,
      currentReport: patientReport || {
        id: `report_${patient.id}_${Date.now()}`,
        patientId: patient.id,
        findings: '',
        diagnosis: '',
        suggestion: '',
        template: '',
        criticalValue: '不上报',
        hpTest: '阴性',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'draft',
      },
      isReportModified: false,
    });
  },
  
  // 设置当前报告
  setCurrentReport: (report: MedicalReport) => {
    set({ currentReport: report, isReportModified: false });
  },
  
  // 更新报告内容
  updateReport: (reportId: string, updates: Partial<MedicalReport>) => {
    const { reports, currentReport } = get();
    
    // 更新报告列表中的报告
    const updatedReports = reports.map(report => 
      report.id === reportId 
        ? { ...report, ...updates, updatedAt: new Date().toISOString() }
        : report
    );
    
    // 如果更新的是当前报告，也更新当前报告
    const updatedCurrentReport = currentReport?.id === reportId 
      ? { ...currentReport, ...updates, updatedAt: new Date().toISOString() }
      : currentReport;
    
    set({
      reports: updatedReports,
      currentReport: updatedCurrentReport,
      isReportModified: true,
    });
  },
  
  // 保存报告
  saveReport: () => {
    const { currentReport, reports } = get();
    if (!currentReport) return;
    
    const existingReportIndex = reports.findIndex(r => r.id === currentReport.id);
    let updatedReports;
    
    if (existingReportIndex >= 0) {
      // 更新现有报告
      updatedReports = [...reports];
      updatedReports[existingReportIndex] = {
        ...currentReport,
        updatedAt: new Date().toISOString(),
        status: 'completed',
      };
    } else {
      // 添加新报告
      updatedReports = [...reports, {
        ...currentReport,
        updatedAt: new Date().toISOString(),
        status: 'completed',
      }];
    }
    
    set({
      reports: updatedReports,
      currentReport: {
        ...currentReport,
        updatedAt: new Date().toISOString(),
        status: 'completed',
      },
      isReportModified: false,
    });
  },
  
  // 设置选中的模板
  setSelectedTemplate: (template: ReportTemplate) => {
    set({ selectedTemplate: template });
  },
  
  // 应用模板
  applyTemplate: () => {
    const { selectedTemplate, currentReport } = get();
    if (!selectedTemplate || !currentReport) return;
    
    const updatedReport = {
      ...currentReport,
      findings: selectedTemplate.findingsTemplate,
      diagnosis: selectedTemplate.diagnosisTemplate,
      suggestion: selectedTemplate.suggestionTemplate,
      template: selectedTemplate.id,
      updatedAt: new Date().toISOString(),
    };
    
    set({
      currentReport: updatedReport,
      isReportModified: true,
    });
  },
  
  // 更新影像查看器设置
  updateViewerSettings: (settings: Partial<ViewerSettings>) => {
    set(state => ({
      viewerSettings: { ...state.viewerSettings, ...settings }
    }));
  },
  
  // 设置报告修改状态
  setReportModified: (modified: boolean) => {
    set({ isReportModified: modified });
  },
}));