import { Patient, MedicalImage, MedicalReport, ReportTemplate } from '../types';

// 模拟患者数据
export const mockPatients: Patient[] = [
  {
    id: '1',
    name: '张三',
    age: 45,
    gender: '男',
    examDate: '2024-01-15',
    examType: '胸部CT',
    studyId: 'CT20240115001',
    chiefComplaint: '胸痛3天，伴有咳嗽，无发热',
  },
  {
    id: '2',
    name: '李四',
    age: 32,
    gender: '女',
    examDate: '2024-01-16',
    examType: '腹部MRI',
    studyId: 'MRI20240116001',
    chiefComplaint: '右上腹疼痛1周，餐后加重',
  },
  {
    id: '3',
    name: '王五',
    age: 58,
    gender: '男',
    examDate: '2024-01-17',
    examType: '头颅CT',
    studyId: 'CT20240117001',
    chiefComplaint: '头痛头晕2天，伴有恶心呕吐',
  },
  {
    id: '4',
    name: '赵六',
    age: 28,
    gender: '女',
    examDate: '2024-01-18',
    examType: '膝关节MRI',
    studyId: 'MRI20240118001',
    chiefComplaint: '左膝关节疼痛肿胀1个月，活动受限',
  },
];

// 模拟医学影像数据
export const mockImages: MedicalImage[] = [
  {
    id: 'img1',
    patientId: '1',
    seriesName: '胸部CT轴位',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=chest%20CT%20scan%20axial%20view%20medical%20imaging%20lungs%20ribs%20spine&image_size=square_hd',
    thumbnailUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=chest%20CT%20scan%20thumbnail&image_size=square',
    sequenceNumber: 1,
  },
  {
    id: 'img2',
    patientId: '2',
    seriesName: '腹部MRI T1加权',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=abdominal%20MRI%20T1%20weighted%20medical%20scan%20liver%20kidney%20organs&image_size=square_hd',
    thumbnailUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=abdominal%20MRI%20thumbnail&image_size=square',
    sequenceNumber: 1,
  },
  {
    id: 'img3',
    patientId: '3',
    seriesName: '头颅CT平扫',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=brain%20CT%20scan%20axial%20view%20medical%20imaging%20skull%20brain%20tissue&image_size=square_hd',
    thumbnailUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=brain%20CT%20thumbnail&image_size=square',
    sequenceNumber: 1,
  },
  {
    id: 'img4',
    patientId: '4',
    seriesName: '膝关节MRI矢状位',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=knee%20MRI%20sagittal%20view%20medical%20scan%20joint%20bones%20cartilage&image_size=square_hd',
    thumbnailUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=knee%20MRI%20thumbnail&image_size=square',
    sequenceNumber: 1,
  },
];

// 模拟报告数据
export const mockReports: MedicalReport[] = [
  {
    id: 'report1',
    patientId: '1',
    findings: '胸部CT平扫显示：\n1. 双肺纹理清晰，未见明显实质性病变\n2. 纵隔结构居中，心影大小正常\n3. 胸膜腔未见积液\n4. 骨质结构未见异常',
    diagnosis: '胸部CT检查未见明显异常',
    suggestion: '建议：\n1. 定期复查\n2. 如有胸痛、咳嗽等症状请及时就诊',
    template: 'chest_ct',
    criticalValue: '不上报',
    hpTest: '阴性',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    status: 'completed',
  },
  {
    id: 'report2',
    patientId: '2',
    findings: '腹部MRI检查显示：\n1. 肝脏形态大小正常，信号均匀\n2. 胆囊壁光滑，未见结石\n3. 胰腺形态正常，胰管未见扩张\n4. 双肾形态正常，皮髓质分界清楚',
    diagnosis: '腹部MRI检查未见明显异常',
    suggestion: '建议：\n1. 继续观察\n2. 保持健康生活方式\n3. 定期体检',
    template: 'abdominal_mri',
    criticalValue: '不上报',
    hpTest: '阳性',
    createdAt: '2024-01-16T14:20:00Z',
    updatedAt: '2024-01-16T14:20:00Z',
    status: 'completed',
  },
  {
    id: 'report3',
    patientId: '3',
    findings: '头颅CT平扫显示：\n1. 脑实质密度正常，灰白质分界清楚\n2. 脑室系统大小形态正常\n3. 中线结构居中\n4. 颅骨完整，未见骨折',
    diagnosis: '头颅CT检查未见明显异常',
    suggestion: '建议：\n1. 注意休息\n2. 避免头部外伤\n3. 如有头痛、头晕等症状加重请及时复诊',
    template: 'head_ct',
    criticalValue: '上报',
    hpTest: '阴性',
    createdAt: '2024-01-17T09:15:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
    status: 'completed',
  },
  {
    id: 'report4',
    patientId: '4',
    findings: '膝关节MRI检查显示：\n1. 股骨、胫骨、腓骨骨质结构正常\n2. 内外侧半月板形态正常\n3. 前后交叉韧带连续性好\n4. 关节腔未见积液',
    diagnosis: '膝关节MRI检查未见明显异常',
    suggestion: '建议：\n1. 适当运动，避免过度负重\n2. 加强膝关节周围肌肉锻炼\n3. 如有疼痛、肿胀等症状请及时就诊',
    template: 'knee_mri',
    criticalValue: '不上报',
    hpTest: '阴性',
    createdAt: '2024-01-18T16:45:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
    status: 'completed',
  },
];

// 模拟报告模板
export const mockTemplates: ReportTemplate[] = [
  {
    id: 'chest_ct',
    name: '胸部CT模板',
    examType: '胸部CT',
    findingsTemplate: '胸部CT检查显示：\n1. 双肺纹理\n2. 纵隔结构\n3. 胸膜腔\n4. 骨质结构',
    diagnosisTemplate: '诊断意见：',
    suggestionTemplate: '建议：\n1. \n2. \n3. ',
  },
  {
    id: 'abdominal_mri',
    name: '腹部MRI模板',
    examType: '腹部MRI',
    findingsTemplate: '腹部MRI检查显示：\n1. 肝脏\n2. 胆囊\n3. 胰腺\n4. 双肾',
    diagnosisTemplate: '诊断意见：',
    suggestionTemplate: '建议：\n1. \n2. \n3. ',
  },
  {
    id: 'head_ct',
    name: '头颅CT模板',
    examType: '头颅CT',
    findingsTemplate: '头颅CT检查显示：\n1. 脑实质\n2. 脑室系统\n3. 中线结构\n4. 颅骨',
    diagnosisTemplate: '诊断意见：',
    suggestionTemplate: '建议：\n1. \n2. \n3. ',
  },
  {
    id: 'knee_mri',
    name: '膝关节MRI模板',
    examType: '膝关节MRI',
    findingsTemplate: '膝关节MRI检查显示：\n1. 骨质结构\n2. 半月板\n3. 韧带\n4. 关节腔',
    diagnosisTemplate: '诊断意见：',
    suggestionTemplate: '建议：\n1. \n2. \n3. ',
  },
];