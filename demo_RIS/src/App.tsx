import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Toaster } from 'sonner';
import Home from "./pages/Home";
import DicomViewer from "./pages/DicomViewer";

export default function App() {
  return (
    <>
      <Router>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/dicom-viewer" element={<DicomViewer />} />
          <Route path="/other" element={<div className="text-center text-xl">Other Page - Coming Soon</div>} />
        </Routes>
      </Router>
      <Toaster 
        position="top-right" 
        richColors 
        closeButton 
        duration={3000}
      />
    </>
  );
}
