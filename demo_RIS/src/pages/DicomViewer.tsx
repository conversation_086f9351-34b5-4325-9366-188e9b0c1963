import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, ZoomIn, ZoomOut, RotateCw, Move, Settings } from 'lucide-react';
import { useReportStore } from '../store/useReportStore';
import { mockImages } from '../data/mockData';

const DicomViewer: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const patientId = searchParams.get('patientId');
  const { patients } = useReportStore();
  
  const [viewerSettings, setViewerSettings] = useState({
    zoom: 1,
    rotation: 0,
    windowWidth: 400,
    windowCenter: 40,
    brightness: 50,
    contrast: 50,
    panX: 0,
    panY: 0
  });
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  
  const patient = patients.find(p => p.id === patientId);
  const patientImages = mockImages.filter(img => img.patientId === patientId);
  const currentImage = patientImages[0]; // 简化处理，只显示第一张图片
  
  useEffect(() => {
    if (!patient) {
      navigate('/');
    }
  }, [patient, navigate]);
  
  const handleZoomIn = () => {
    setViewerSettings(prev => ({ ...prev, zoom: Math.min(prev.zoom * 1.2, 5) }));
  };
  
  const handleZoomOut = () => {
    setViewerSettings(prev => ({ ...prev, zoom: Math.max(prev.zoom / 1.2, 0.1) }));
  };
  
  const handleRotate = () => {
    setViewerSettings(prev => ({ ...prev, rotation: (prev.rotation + 90) % 360 }));
  };
  
  const handleReset = () => {
    setViewerSettings({
      zoom: 1,
      rotation: 0,
      windowWidth: 400,
      windowCenter: 40,
      brightness: 50,
      contrast: 50,
      panX: 0,
      panY: 0
    });
  };
  
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - viewerSettings.panX, y: e.clientY - viewerSettings.panY });
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setViewerSettings(prev => ({
        ...prev,
        panX: e.clientX - dragStart.x,
        panY: e.clientY - dragStart.y
      }));
    }
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setViewerSettings(prev => ({ ...prev, zoom: Math.max(0.1, Math.min(5, prev.zoom * delta)) }));
  };
  
  if (!patient || !currentImage) {
    return (
      <div className="h-screen bg-black flex items-center justify-center text-white">
        <div className="text-center">
          <p className="text-xl mb-4">未找到患者影像</p>
          <button 
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            返回主页
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="h-screen bg-black flex flex-col text-white">
      {/* 顶部工具栏 */}
      <div className="bg-gray-900 p-4 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <button 
            onClick={() => navigate('/')}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回报告</span>
          </button>
          
          <div className="text-sm">
            <span className="font-medium">{patient.name}</span>
            <span className="mx-2">·</span>
            <span>{patient.examType}</span>
            <span className="mx-2">·</span>
            <span className="text-gray-400">{patient.studyId}</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button 
            onClick={handleZoomOut}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
            title="缩小"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          
          <span className="text-sm px-2">{Math.round(viewerSettings.zoom * 100)}%</span>
          
          <button 
            onClick={handleZoomIn}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
            title="放大"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          
          <button 
            onClick={handleRotate}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors"
            title="旋转"
          >
            <RotateCw className="w-4 h-4" />
          </button>
          
          <button 
            onClick={handleReset}
            className="px-3 py-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors text-sm"
          >
            重置
          </button>
        </div>
      </div>
      
      {/* 主要影像显示区域 */}
      <div className="flex-1 relative overflow-hidden">
        <div 
          className="w-full h-full flex items-center justify-center cursor-move"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
        >
          <img 
            src={currentImage.imageUrl}
            alt={currentImage.seriesName}
            className="max-w-none select-none"
            style={{
              transform: `scale(${viewerSettings.zoom}) rotate(${viewerSettings.rotation}deg) translate(${viewerSettings.panX}px, ${viewerSettings.panY}px)`,
              filter: `brightness(${viewerSettings.brightness}%) contrast(${viewerSettings.contrast}%)`,
              transition: isDragging ? 'none' : 'transform 0.1s ease-out'
            }}
            draggable={false}
          />
        </div>
        
        {/* 影像信息叠加 */}
        <div className="absolute top-4 left-4 bg-black bg-opacity-50 p-3 rounded text-sm">
          <div>患者: {patient.name}</div>
          <div>检查: {currentImage.seriesName}</div>
          <div>日期: {patient.examDate}</div>
          <div>ID: {patient.studyId}</div>
        </div>
        
        {/* 窗宽窗位信息 */}
        <div className="absolute top-4 right-4 bg-black bg-opacity-50 p-3 rounded text-sm">
          <div>窗宽: {viewerSettings.windowWidth}</div>
          <div>窗位: {viewerSettings.windowCenter}</div>
          <div>缩放: {Math.round(viewerSettings.zoom * 100)}%</div>
        </div>
        
        {/* 操作提示 */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 p-3 rounded text-xs text-gray-300">
          <div>鼠标滚轮: 缩放</div>
          <div>拖拽: 平移</div>
          <div>工具栏: 旋转、重置</div>
        </div>
      </div>
    </div>
  );
};

export default DicomViewer;