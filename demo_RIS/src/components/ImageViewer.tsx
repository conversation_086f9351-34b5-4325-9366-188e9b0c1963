import React, { useState, useRef, useEffect } from 'react';
import { ZoomIn, ZoomOut, RotateCw, Move, Settings, Maximize2 } from 'lucide-react';
import { useReportStore } from '../store/useReportStore';
import { mockImages } from '../data/mockData';

const ImageViewer: React.FC = () => {
  const { selectedPatient, viewerSettings, updateViewerSettings } = useReportStore();
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [showSettings, setShowSettings] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 获取当前患者的影像
  const currentImage = selectedPatient 
    ? mockImages.find(img => img.patientId === selectedPatient.id)
    : null;

  // 重置图像位置和缩放
  const resetView = () => {
    setImagePosition({ x: 0, y: 0 });
    updateViewerSettings({ zoom: 1.0 });
  };

  // 缩放控制
  const handleZoom = (delta: number) => {
    const newZoom = Math.max(0.1, Math.min(5.0, viewerSettings.zoom + delta));
    updateViewerSettings({ zoom: newZoom });
  };

  // 鼠标拖拽开始
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - imagePosition.x, y: e.clientY - imagePosition.y });
  };

  // 鼠标拖拽移动
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    setImagePosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  // 鼠标拖拽结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 鼠标滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    handleZoom(delta);
  };

  // 窗宽窗位调节
  const handleWindowLevelChange = (type: 'width' | 'center', value: number) => {
    updateViewerSettings({
      [type === 'width' ? 'windowWidth' : 'windowCenter']: value
    });
  };

  // 亮度对比度调节
  const handleDisplayChange = (type: 'brightness' | 'contrast', value: number) => {
    updateViewerSettings({ [type]: value });
  };

  // 当选择新患者时重置视图
  useEffect(() => {
    resetView();
  }, [selectedPatient]);

  return (
    <div className="flex-1 bg-black relative flex flex-col">
      {/* 工具栏 */}
      <div className="bg-gray-800 text-white p-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* 缩放控制 */}
          <button
            onClick={() => handleZoom(-0.2)}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="缩小"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          <span className="text-sm px-2">
            {Math.round(viewerSettings.zoom * 100)}%
          </span>
          <button
            onClick={() => handleZoom(0.2)}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="放大"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          
          {/* 重置视图 */}
          <button
            onClick={resetView}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="重置视图"
          >
            <Maximize2 className="w-4 h-4" />
          </button>
          
          {/* 移动工具 */}
          <button
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="移动工具"
          >
            <Move className="w-4 h-4" />
          </button>
        </div>

        {/* 患者信息 */}
        {selectedPatient && (
          <div className="text-sm">
            <span>{selectedPatient.name} - {selectedPatient.examType}</span>
            <span className="ml-4 text-gray-400">{selectedPatient.studyId}</span>
          </div>
        )}

        {/* 设置按钮 */}
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="显示设置"
        >
          <Settings className="w-4 h-4" />
        </button>
      </div>

      {/* 主显示区域 */}
      <div 
        ref={containerRef}
        className="flex-1 relative overflow-hidden cursor-move"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      >
        {currentImage ? (
          <img
            ref={imageRef}
            src={currentImage.imageUrl}
            alt={currentImage.seriesName}
            className="absolute top-1/2 left-1/2 max-w-none select-none"
            style={{
              transform: `translate(-50%, -50%) translate(${imagePosition.x}px, ${imagePosition.y}px) scale(${viewerSettings.zoom})`,
              filter: `brightness(${viewerSettings.brightness}%) contrast(${viewerSettings.contrast}%)`,
              cursor: isDragging ? 'grabbing' : 'grab'
            }}
            draggable={false}
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-lg flex items-center justify-center">
                <Move className="w-8 h-8" />
              </div>
              <p className="text-lg mb-2">请选择患者查看影像</p>
              <p className="text-sm text-gray-400">从左侧患者列表中选择一位患者</p>
            </div>
          </div>
        )}
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <div className="absolute top-12 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg w-80 z-10">
          <h3 className="text-lg font-semibold mb-4">影像设置</h3>
          
          {/* 窗宽窗位 */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">窗宽</label>
            <input
              type="range"
              min="50"
              max="2000"
              value={viewerSettings.windowWidth}
              onChange={(e) => handleWindowLevelChange('width', parseInt(e.target.value))}
              className="w-full"
            />
            <span className="text-sm text-gray-400">{viewerSettings.windowWidth}</span>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">窗位</label>
            <input
              type="range"
              min="-1000"
              max="1000"
              value={viewerSettings.windowCenter}
              onChange={(e) => handleWindowLevelChange('center', parseInt(e.target.value))}
              className="w-full"
            />
            <span className="text-sm text-gray-400">{viewerSettings.windowCenter}</span>
          </div>
          
          {/* 亮度对比度 */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">亮度</label>
            <input
              type="range"
              min="0"
              max="200"
              value={viewerSettings.brightness}
              onChange={(e) => handleDisplayChange('brightness', parseInt(e.target.value))}
              className="w-full"
            />
            <span className="text-sm text-gray-400">{viewerSettings.brightness}%</span>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">对比度</label>
            <input
              type="range"
              min="0"
              max="200"
              value={viewerSettings.contrast}
              onChange={(e) => handleDisplayChange('contrast', parseInt(e.target.value))}
              className="w-full"
            />
            <span className="text-sm text-gray-400">{viewerSettings.contrast}%</span>
          </div>
          
          <button
            onClick={() => setShowSettings(false)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
          >
            关闭设置
          </button>
        </div>
      )}

      {/* 影像信息覆盖层 */}
      {currentImage && (
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-2 rounded text-sm">
          <div>{currentImage.seriesName}</div>
          <div className="text-gray-300">序列 {currentImage.sequenceNumber}</div>
        </div>
      )}
    </div>
  );
};

export default ImageViewer;