import React, { useState } from 'react';
import { Search, User, Calendar, FileText } from 'lucide-react';
import { useReportStore } from '../store/useReportStore';
import { Patient } from '../types';

const PatientPanel: React.FC = () => {
  const { patients, selectedPatient, setSelectedPatient } = useReportStore();
  const [searchTerm, setSearchTerm] = useState('');

  // 过滤患者列表
  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.examType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* 标题栏 */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">患者列表</h2>
        
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="搜索患者姓名或检查类型"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>
      </div>

      {/* 患者列表 */}
      <div className="flex-1 overflow-y-auto">
        {filteredPatients.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <User className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>未找到匹配的患者</p>
          </div>
        ) : (
          <div className="p-2">
            {filteredPatients.map((patient) => (
              <div
                key={patient.id}
                onClick={() => handlePatientSelect(patient)}
                className={`p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                  selectedPatient?.id === patient.id
                    ? 'bg-blue-50 border-2 border-blue-200 shadow-sm'
                    : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                {/* 患者基本信息 */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <User className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-800">{patient.name}</h3>
                      <p className="text-sm text-gray-500">
                        {patient.gender} · {patient.age}岁
                      </p>
                    </div>
                  </div>
                  {selectedPatient?.id === patient.id && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>

                {/* 检查信息 */}
                <div className="space-y-1">
                  <div className="flex items-center text-sm text-gray-600">
                    <FileText className="w-4 h-4 mr-2" />
                    <span>{patient.examType}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>{formatDate(patient.examDate)}</span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    <span className="text-xs text-gray-500">主诉:</span>
                    <span className="ml-1">{patient.chiefComplaint}</span>
                  </div>
                </div>

                {/* 检查ID */}
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500 font-mono">
                    ID: {patient.studyId}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部统计信息 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-sm text-gray-600">
          <span>共 {filteredPatients.length} 位患者</span>
          {searchTerm && (
            <span className="ml-2 text-blue-600">
              (搜索: "{searchTerm}")
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientPanel;