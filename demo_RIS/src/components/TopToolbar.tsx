import React from 'react';
import { Save, Printer, FileText, Settings, User, LogOut } from 'lucide-react';
import { useReportStore } from '../store/useReportStore';
import { toast } from 'sonner';

const TopToolbar: React.FC = () => {
  const { selectedPatient, currentReport, saveReport, isReportModified } = useReportStore();

  // 保存报告
  const handleSave = () => {
    if (!currentReport) {
      toast.error('请先选择患者');
      return;
    }
    saveReport();
    toast.success('报告已保存');
  };

  // 打印报告
  const handlePrint = () => {
    if (!currentReport || !selectedPatient) {
      toast.error('请先选择患者并编写报告');
      return;
    }
    
    // 创建打印内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>医疗影像报告 - ${selectedPatient.name}</title>
        <style>
          body { font-family: 'Microsoft YaHei', sans-serif; margin: 40px; line-height: 1.6; }
          .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
          .patient-info { background: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
          .section { margin-bottom: 25px; }
          .section-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; color: #333; }
          .content { white-space: pre-wrap; line-height: 1.8; }
          .footer { margin-top: 40px; text-align: right; }
          @media print { body { margin: 20px; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>医疗影像诊断报告</h1>
        </div>
        
        <div class="patient-info">
          <h3>患者信息</h3>
          <p><strong>姓名：</strong>${selectedPatient.name}</p>
          <p><strong>性别：</strong>${selectedPatient.gender}</p>
          <p><strong>年龄：</strong>${selectedPatient.age}岁</p>
          <p><strong>检查类型：</strong>${selectedPatient.examType}</p>
          <p><strong>检查日期：</strong>${selectedPatient.examDate}</p>
          <p><strong>检查号：</strong>${selectedPatient.studyId}</p>
        </div>
        
        <div class="section">
          <div class="section-title">检查所见</div>
          <div class="content">${currentReport.findings || '暂无内容'}</div>
        </div>
        
        <div class="section">
          <div class="section-title">诊断意见</div>
          <div class="content">${currentReport.diagnosis || '暂无内容'}</div>
        </div>
        
        <div class="section">
          <div class="section-title">建议</div>
          <div class="content">${currentReport.suggestion || '暂无内容'}</div>
        </div>
        
        <div class="footer">
          <p>报告日期：${new Date().toLocaleDateString('zh-CN')}</p>
          <p>影像科医生：_______________</p>
        </div>
      </body>
      </html>
    `;
    
    // 打开新窗口并打印
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
    
    toast.success('报告已发送到打印机');
  };

  // 导出报告
  const handleExport = () => {
    if (!currentReport || !selectedPatient) {
      toast.error('请先选择患者并编写报告');
      return;
    }
    
    const reportData = {
      patient: selectedPatient,
      report: currentReport,
      exportTime: new Date().toISOString()
    };
    
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${selectedPatient.name}_${selectedPatient.examType}_${selectedPatient.examDate}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast.success('报告已导出');
  };

  return (
    <div className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6">
      {/* 左侧 - 系统标题 */}
      <div className="flex items-center">
        <div className="flex items-center mr-8">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-xl font-semibold text-gray-800">医疗影像报告系统</h1>
        </div>
        
        {/* 当前患者信息 */}
        {selectedPatient && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">{selectedPatient.name}</span>
            <span className="mx-2">·</span>
            <span>{selectedPatient.examType}</span>
          </div>
        )}
      </div>

      {/* 中间 - 操作按钮 */}
      <div className="flex items-center space-x-3">
        <button
          onClick={handleSave}
          disabled={!isReportModified || !currentReport}
          className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
            isReportModified && currentReport
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="保存报告"
        >
          <Save className="w-4 h-4 mr-2" />
          保存
          {isReportModified && (
            <span className="ml-2 w-2 h-2 bg-orange-400 rounded-full"></span>
          )}
        </button>
        
        <button
          onClick={handlePrint}
          disabled={!currentReport || !selectedPatient}
          className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
            currentReport && selectedPatient
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="打印报告"
        >
          <Printer className="w-4 h-4 mr-2" />
          打印
        </button>
        
        <button
          onClick={handleExport}
          disabled={!currentReport || !selectedPatient}
          className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
            currentReport && selectedPatient
              ? 'bg-purple-600 hover:bg-purple-700 text-white'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="导出报告"
        >
          <FileText className="w-4 h-4 mr-2" />
          导出
        </button>
      </div>

      {/* 右侧 - 用户信息和设置 */}
      <div className="flex items-center space-x-3">
        <button
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          title="系统设置"
        >
          <Settings className="w-5 h-5" />
        </button>
        
        <div className="flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg">
          <User className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-700">影像科医生</span>
        </div>
        
        <button
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          title="退出登录"
          onClick={() => toast.info('退出登录功能')}
        >
          <LogOut className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};

export default TopToolbar;